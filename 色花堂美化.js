// ==UserScript==
// @name         ☆色花堂美化☆
// @description  色花堂/98堂论坛网页桌面端美化
// @namespace    https://www.sehuatang.net
// @version      0.8
// <AUTHOR>
// @match        *://*.sehuatang.*/*
// @match        *://*.sehuatang.net/*
// @match        *://*.sehuatang.org/*
// @match        *://*.pbvfx.*/*
// @run-at       document-start
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_registerMenuCommand
// @license      GPL-3.0 License
// ==/UserScript==

// 1. 初始隐藏页面，防止闪烁，并在脚本准备好后显示
const initialHideStyle = document.createElement('style');
initialHideStyle.textContent = `html { visibility: hidden !important; opacity: 0 !important; transition: opacity 0.3s ease !important; } html.sht-script-ready { visibility: visible !important; opacity: 1 !important; }`;
document.documentElement.appendChild(initialHideStyle);

// 2. 创建和插入基础样式（立即执行，不依赖DOM）
const baseStyle = document.createElement('style');
baseStyle.type = 'text/css';
baseStyle.innerHTML = `
html, body {
  margin: 0;
  background: none !important;
  background-image: none !important;
  position: relative;
}

.gradient-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
  background: #fff;

}

.gradient-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--background-color);
  filter: saturate(var(--saturation-level));

}

.gradient-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--background-overlay);
  z-index: 1;
}
  `;
document.documentElement.appendChild(baseStyle);


// 3. 立即应用主题Class (在基础样式之后，以便CSS变量生效)
(function() {
  let theme = null;
  try {
      if (typeof GM_getValue === 'function') {
          theme = GM_getValue('sehuatang_theme', 'pink');
      } else {
          theme = localStorage.getItem('sehuatang_theme') || 'pink';
      }
  } catch(e) {
      // 主题读取错误，使用默认主题
  }

  // 应用主题
  if (theme === 'green') {
      document.documentElement.classList.add('theme-green');
      document.documentElement.classList.remove('theme-pink', 'theme-light', 'theme-dark');
  } else if (theme === 'light') {
      document.documentElement.classList.add('theme-light');
      document.documentElement.classList.remove('theme-pink', 'theme-green', 'theme-dark');
  } else if (theme === 'dark') {
      document.documentElement.classList.add('theme-dark');
      document.documentElement.classList.remove('theme-pink', 'theme-green', 'theme-light');
  } else {
      document.documentElement.classList.add('theme-pink');
      document.documentElement.classList.remove('theme-green', 'theme-light', 'theme-dark');
  }
})();

(function() {
  'use strict';

  // --- Optimized enableDrag function ---
  /**
   * 启用拖动
   * @param {HTMLElement} elmnt - 要启用拖动的元素
   */
  function enableDrag(elmnt) {
      // Check if drag is already enabled
      if (elmnt.dataset.dragEnabled) {
          // Drag already enabled
          return;
      }
      // Enabling drag
      elmnt.dataset.dragEnabled = 'true'; // Mark as enabled

      var initialX = 0, initialY = 0, currentX = 0, currentY = 0, offsetX = 0, offsetY = 0;
      var latestMouseX = 0, latestMouseY = 0;
      var rafId = null;
      var isDragging = false;

      // Use the element itself as the drag handle initially.
      // Check for custom drag handle selector in dataset
      var dragHandle = elmnt;
      if (elmnt.dataset.dragHandle) {
          var customHandle = elmnt.querySelector(elmnt.dataset.dragHandle);
          if (customHandle) {
              dragHandle = customHandle;
          }
      } else {
          // Discuz popups often have a header with class 'flb', which is better.
          var flbHandle = elmnt.querySelector('.flb');
          if (flbHandle) {
              dragHandle = flbHandle;
          }
      }
      // Ensure the drag handle has a cursor style indicating it's draggable
      dragHandle.style.cursor = 'move';

      dragHandle.onmousedown = dragMouseDown;

      function dragMouseDown(e) {
          e = e || window.event;
          const target = e.target;

          // Prevent dragging on interactive elements inside the handle or element
          const isInteractive = target.tagName.match(/^(INPUT|TEXTAREA|BUTTON|SELECT|OPTION|LABEL|A)$/) ||
                                target.closest('button, a, input, textarea, select') !== null ||
                                target.classList.contains('flbc'); // Exclude the close button often found in .flb

          if (isInteractive) {
              // Clicked on interactive element, drag prevented
              return;
          }

          // Prevent default text selection behavior only when starting drag on the handle
          e.preventDefault();

          initialX = e.clientX;
          initialY = e.clientY;
          latestMouseX = initialX;
          latestMouseY = initialY;

          // Get position using getBoundingClientRect for accuracy with fixed positioning
          const rect = elmnt.getBoundingClientRect();
          currentX = rect.left;
          currentY = rect.top;

          offsetX = initialX - currentX;
          offsetY = initialY - currentY;

          // Ensure element has fixed or absolute positioning for top/left to work reliably
          const currentPosition = window.getComputedStyle(elmnt).position;
          if (currentPosition !== 'fixed' && currentPosition !== 'absolute') {
             // Draggable element is not fixed or absolute, setting to fixed
             elmnt.style.position = 'fixed'; // Force fixed if not already
             // Re-calculate position after potentially changing position type
             const newRect = elmnt.getBoundingClientRect();
             currentX = newRect.left;
             currentY = newRect.top;
             offsetX = initialX - currentX;
             offsetY = initialY - currentY;
          }

          // Remove transform if it was used for centering, set top/left explicitly
          elmnt.style.transform = 'none';
          elmnt.style.left = currentX + 'px';
          elmnt.style.top = currentY + 'px';
          // Ensure margin is reset if transform was used for centering
          elmnt.style.marginLeft = '0';
          elmnt.style.marginTop = '0';

          isDragging = true;

          document.addEventListener('mouseup', closeDragElement, true); // Use capture phase
          document.addEventListener('mousemove', elementDrag, false); // Use bubbling phase
      }

      function elementDrag(e) {
          if (!isDragging) return;

          e = e || window.event;

          latestMouseX = e.clientX;
          latestMouseY = e.clientY;

          scheduleUpdate();
      }

      function scheduleUpdate() {
          if (rafId === null) {
              rafId = requestAnimationFrame(updateElementPosition);
          }
      }

      function updateElementPosition() {
          if (!isDragging) {
              rafId = null;
              return;
          }

          const newPosX = latestMouseX - offsetX;
          const newPosY = latestMouseY - offsetY;

          elmnt.style.left = newPosX + 'px';
          elmnt.style.top = newPosY + 'px';

          rafId = null;
      }

      function closeDragElement() {
          if (isDragging) {
              isDragging = false;

              if (rafId !== null) {
                  cancelAnimationFrame(rafId);
                  rafId = null;
              }

              document.removeEventListener('mouseup', closeDragElement, true); // Match capture phase
              document.removeEventListener('mousemove', elementDrag, false); // Match bubbling phase

              // Optional: Mark that it has been dragged
              // elmnt.dataset.hasBeenDragged = "true";
          }
      }
  }
  // --- End of enableDrag function ---

  // 定义 ready 函数
  function ready(fn) {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', fn);
    } else {
      fn();
    }
  }

  // 待页面结构开始形成时，添加背景层
  const addBackgroundLayer = () => {
      // 如果在 iframe 中运行，则不添加背景层
      if (window.self !== window.top) {
          // Inside iframe, skipping background layer
          return false;
      }
      if (document.body) {
          if (!document.querySelector('.gradient-background')) {
              const gradientDiv = document.createElement('div');
              gradientDiv.className = 'gradient-background';
              document.body.appendChild(gradientDiv);
          }
          return true;
      }
      return false;
  };

  // 尝试添加背景层，如果DOM还未准备好，则设置观察器
  if (!addBackgroundLayer()) {
      const bodyObserver = new MutationObserver(() => {
          if (addBackgroundLayer()) {
              bodyObserver.disconnect();
          }
      });
      bodyObserver.observe(document.documentElement, { childList: true, subtree: true });
  }


  const STYLE_ID = "sehuatang-enhanced-styles";

  const customStyles = `

/* 全局圆角 */
* {
  font-family: "PingFang SC","Noto Sans SC","Microsoft YaHei","SF Pro", Arial, sans-serif  !important;
  font-weight: 500 !important;
  border-radius: 10px;
  text-decoration: none !important;
}

body > * {
     border-radius: 0;
}

:root {
  --icon-radius: 8px;
}

/* 粉色主题 */
.theme-pink {
--primary-color: rgba(230, 0, 23, 0.5);
--primary-font-color: #BD5660;
--Second-color: rgba(236, 162, 162, 0.5);
--background-color: radial-gradient(at 40% 20%, rgba(240, 178, 141, 0.24) 0px, transparent 70%), radial-gradient(at 80% 0%, rgba(217, 129, 129, 0.5) 0px, transparent 40%), radial-gradient(at 0% 50%, rgba(234, 168, 129, 0.48) 0px, transparent 70%), radial-gradient(at 80% 50%, rgba(229, 87, 167, 0.5) 0px, transparent 70%), radial-gradient(at 0% 100%, rgba(200, 116, 200, 0.6) 0px, transparent 70%), radial-gradient(at 80% 100%, rgba(135, 87, 229, 0.5) 0px, transparent 70%), radial-gradient(at 0% 0%, rgba(253, 65, 135, 0.5) 0px, transparent 70%) !important;
--background-overlay: rgba(0, 0, 0, 0.00);
--sider-color: rgba(255, 203, 203, 0.13);
--sider-line: 0px solid #fcc;
--postlist-color: rgba(255, 255, 255, 0.7);
--tedt-bar-color: rgba(249, 222, 222, 0.2);
--td-color: #FFF7F7;
--tag: #F995A3;
--button: rgba(136, 55, 97, 0.5);
--a-font-color: rgba(0, 0, 0, 0.8);
--list-color: var(--light-list-background-color);
--list-color2: var(--light-list-background-color2);
--list-border: var(--light-list-border-color);
--avatar-border: var(--light-avatar-border-color);
--img-border: var(--light-img-border-color);
}

/* 绿色主题 */
.theme-green {
--primary-color: #7CA6AF;
--background-color: rgba(109, 159, 172, 0.6);
--Second-color: #B6CDCD;
--primary-font-color: #3a5564;
--sider-color: #e1e9ea;
--sider-line: none;
--postlist-color: rgba(255, 255, 255, 0.7);
--tedt-bar-color: rgba(182, 205, 205, 0.2);
--td-color: #E5ECEC;
--tag: #7CA6AF;
--button: rgba(58, 105, 115, 0.5);
--a-font-color: rgba(0, 0, 0, 0.8);
--list-color: var(--light-list-background-color);
--list-color2: var(--light-list-background-color2);
--list-border: var(--light-list-border-color);
--avatar-border: var(--light-avatar-border-color);
--img-border: var(--light-img-border-color);
}

/* 浅色主题 */
.theme-light {
--primary-color: rgba(94, 96, 104, 0.5);
--background-color: radial-gradient(at 30% 20%, rgba(200, 180, 177, 0.7) 0px, transparent 50%),
  radial-gradient(at 80% 10%, rgba(205, 160, 158, 0.6) 0px, transparent 50%),
  radial-gradient(at 10% 80%, rgba(130, 126, 160, 0.6) 0px, transparent 50%),
  radial-gradient(at 70% 70%, rgba(110, 140, 160, 0.7) 0px, transparent 50%);
--background-overlay: rgba(0, 0, 0, 0.03);
--Second-color: rgba(0, 0, 0, 0.1);
--primary-font-color: #374151;
--sider-color: #F3F4F6;
--sider-line: none;
--postlist-color: rgba(255, 255, 255, 0.9);
--tedt-bar-color: rgba(229, 231, 235, 0.3);
--td-color: #F9FAFB;
--tag: #9CA3AF;
--button: rgba(107, 114, 128, 0.5);
--a-font-color: rgba(0, 0, 0, 0.8);
--list-color: var(--light-list-background-color);
--list-color2: var(--light-list-background-color2);
--list-border: var(--light-list-border-color);
--avatar-border: var(--light-avatar-border-color);
--img-border: var(--light-img-border-color);
}

/* 暗色主题 */
.theme-dark {
--primary-color: rgba(0, 0, 0, 0.4);
--primary-font-color: rgba(255, 255, 255, 0.7);
--a-font-color: rgba(255, 255, 255, 0.7);
--background-color: radial-gradient(at 40% 20%, rgba(240, 178, 141, 0.24) 0px, transparent 70%), radial-gradient(at 80% 0%, rgba(217, 129, 129, 0.5) 0px, transparent 40%), radial-gradient(at 0% 50%, rgba(234, 168, 129, 0.48) 0px, transparent 70%), radial-gradient(at 80% 50%, rgba(229, 87, 167, 0.5) 0px, transparent 70%), radial-gradient(at 0% 100%, rgba(200, 116, 200, 0.6) 0px, transparent 70%), radial-gradient(at 80% 100%, rgba(135, 87, 229, 0.5) 0px, transparent 70%), radial-gradient(at 0% 0%, rgba(253, 65, 135, 0.5) 0px, transparent 70%) !important;
--background-overlay: rgba(0, 0, 0, 0.60);
--Second-color: rgba(75, 85, 99, 0.6);
--sider-color: rgba(55, 65, 81, 0.8);
--sider-line: 1px solid rgba(75, 85, 99, 0.3);
--postlist-color: rgba(31, 41, 55, 0.85);
--tedt-bar-color: rgba(55, 65, 81, 0.4);
--td-color: rgba(17, 24, 39, 0.9);
--tag: rgba(255, 255, 255, 0.2);
--button: rgba(0, 0, 0, 0.4);
--saturation-level: 150%;
--list-color: var(--dark-list-background-color);
--list-color2: var(--dark-list-background-color2);
--list-border: var(--dark-list-border-color);
--img-border: var(--dark-img-border-color);
--avatar-border: var(--dark-img-border-color);
--hover-border: var(--dark-hover-border-color);
}

* {
  --light-list-background-color: rgba(255, 255, 255, 0.5);
  --light-list-background-color2: rgba(255, 255, 255, 0.8);
  --light-list-border-color: rgba(255, 255, 255, 0.3);
  --light-avatar-border-color: rgba(0, 0, 0, 0.9);
  --light-img-border-color: rgba(0, 0, 0, 0.2);

  --dark-list-background-color: rgba(0, 0, 0, 0.4);
  --dark-list-background-color2: rgba(0, 0, 0, 0.5);
  --dark-list-border-color: rgba(255, 255, 255, 0.0);
  --dark-img-border-color: rgba(255, 255, 255, 0.2);
  --dark-hover-border-color: rgba(255, 255, 255, 0.3);

}


button {
  cursor: pointer !important;
}

a {
	color: var(--a-font-color);
	text-decoration: none;
}

a:hover {
  text-shadow: 0 0px 10px rgba(255, 255, 255, 0.1);
  color: red !important;
}

/* 回到顶端按钮 */
#scrolltop {
	display: none;
}

/* ------------------------------杂项-------------------------------------- */
.livethreadtitle img ,
.bm_h .o,
.tb.cl.mbw a,
#f_pst,
#pgt,
#aimg_gGJyx,
.wp.mtn,
#wp > div:nth-child(8),
img[src*="static/image/hrline/"][src$=".gif"],
img[src*="static/image/common/hot_"][src$=".gif"],
img[src*="static/image/filetype/"][src$=".gif"],
img[src*="static/image/hrline/"][src$=".gif"],
img[src*="static/image/common/close.gif"],
img[src*="static/image/common/forum_new.gif"],
img[src*="static/image/common/forum.gif"],
img[src*="static/image/common/agree.gif"],
img[src*="static/image/common/logo.png"],
img[src*="static/image/common/logo_sc_s.png"],
img[src*="static/image/feed/thread.gif"],
img[src*="static/image/common/faq.gif"],
img[src*="static/image/feed/discuz.gif"],
img[src*="www.sehuatang.net/template/default/style/t1/bgimg.jpg"],
img[src*="tupian/forum/202502/02/121629ufzw3n4wvvinail2.gif"],
img[src*="tupian/forum/202502/02/122015fxzvv3xec1r9n75v.gif"],
img[src*="cdn.jsdelivr.net/gh/master-of-forums/master-of-forums/public/images/patch.gif"],
[id^="threadlisttableid"] > tbody:nth-child(1) > tr > td.icn > img,
[id^="stickthread"] > tr > td.icn > a > img,
[id^="normalthread_"] > tr > td.icn > a > img,
#scbar_btn_td,
#scbar_hot_td,
#scbar_hot,
#mn_portal,
#mn_Ne7b9,
#ajaxwaitid,
#ak_rate > i > img,

#ak_reportad,
#atarget,
#autopbn,
#diy_chart .frame-1-2-r::after,
#favatar34006704 > p.md_ctrl,
#fj,
#k_favorite > i > img,
#notice_8953983 > dd.ntc_body > div,
#postlist > table.plhin,
#qmenu,
.returnlist,
.replyfast,
#tip,
#toptb,
.ad,
.returnboard,
.notice_pm, .notice_mypost, .notice_interactive, .notice_system, .notice_manage, .notice_app, .alert_btnleft,
.forumrefresh,
.prompt_follower_0,
.prompt_news_0,
.sign,
.span.none,
.tip_4 .tip_horn,
.vwthd div.y,
span.ddpc_borderright,
div > div.card_mn > div.c > p > img,
div.i.y > div.imicn > a > img,
div:nth-child(1) > p:nth-child(2) > a:nth-child(2) > img,
div:nth-child(1) > p:nth-child(2) > a:nth-child(3) > img,
h2 > img {
   display: none !important;
}

.fastre, .replyadd, .replysubtract  {
   background: none !important;
}

.hin {
	opacity: 0;
}

/* ---------隐藏 ---------*/
#separatorline > tr {
    visibility: hidden;
}

.alert_error,
.notice,
.unfold, .fold,
.fa_fav,
.vwmy,
.ct2_a, .ct3_a,
#scform_tb .a,
#myprompt.new,
#nv li, #nv li a:hover, #nv li.hover a, #nv li.hover a:hover {
  background-image: none !important;
}

/* 用项目列表符号代替图片 */
[id^="stickthread"] > tr > td.icn > a,
[id^="normalthread_"] > tr > td.icn > a {
    position: relative;
}

[id^="stickthread"] > tr > td.icn > a::before, [id^="normalthread_"] > tr > td.icn > a::before {
	content: "●";
	font-size: 12px !important;
	color: rgba(0, 0, 0, 0.1);
	position: absolute;
	top: -11px;
	left: 10px;
}

.ttp {
	padding-top: 10px;
	border: none !important;
	background: transparent;
}

.tl tr:hover th, .tl tr:hover td {
    background: transparent;
}

.tl th em, .tl th em a {
color: #1598f5!important;
}

/* 标题 背景 */
.ts {
background: transparent;
}

#tbody > tr:nth-child(1) > td.plc {
border-radius: 0 !important;
}

#ct > div.mn > div.fl.bm > div:nth-child(1) > div.bm_h.cl > h2 > a {
  color: #fff !important;
  margin-left: 10px;
  font-size: 14px;
}

table tbody tr td dl dt {
font-size: 13px;
}

.rfm {
	margin: 10px auto;
	border-bottom: none;
}

/* ----------原始头像-------- */
.pls .avatar {
	margin: 10px auto;
	text-align: center;
}
.pls .avatar img {
	padding: 0px;
	width: 100px;
	outline: 4px solid var(--primary-color);
  border-radius: 14px;
}

/* ---------看帖页背景-------------- */
.plc {
  background-color: #fff;
}

.showmenu {
  padding-right: 10px; /* 保持间距 */
  white-space: nowrap;
  position: relative; /* 确保伪元素定位相对于父元素 */
  background: none;
}

.showmenu::after {
  content: "▼"; /* 替换背景图片为字符 */
  position: absolute; /* 绝对定位伪元素 */
  right: 0; /* 定位到右边 */
  top: 50%; /* 垂直居中 */
  transform: translateY(-50%); /* 将箭头放在弹窗下边缘外 */
  font-size: 7px; /* 根据需求调整大小 */
  color: inherit; /* 确保字符颜色与文字颜色一致 */
}

/* 主页小图标 */
.nvhm {
width: 16px;
background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDUxMiA1MTIiPjxwYXRoIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzAwMDAwMCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2Utd2lkdGg9IjMyIiBkPSJNODAgMjEydjIzNmExNiAxNiAwIDAgMCAxNiAxNmg5NlYzMjhhMjQgMjQgMCAwIDEgMjQtMjRoODBhMjQgMjQgMCAwIDEgMjQgMjR2MTM2aDk2YTE2IDE2IDAgMCAwIDE2LTE2VjIxMiIvPjxwYXRoIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzAwMDAwMCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2Utd2lkdGg9IjMyIiBkPSJNNDgwIDI1NkwyNjYuODkgNTJjLTUtNS4yOC0xNi42OS01LjM0LTIxLjc4IDBMMzIgMjU2bTM2OC03N1Y2NGgtNDh2NjkiLz48L3N2Zz4=) no-repeat center center;
background-size: contain;
line-height: 200px;
overflow: hidden;
}

.fl_icn_g a {
  display: inline-block;
  width: 25px;
  height: 25px;
  margin: 1px 0 0 5px;
  background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MDAgNTAwIj4KICA8cGF0aCBkPSJNIDEzOCA0Mi4wMDEgQyAxMjkuMTY0IDQyLjAwMSAxMjIgNDkuMTY0IDEyMiA1OC4wMDEgTCAxMjIgMTIyLjAwMSBMIDk4IDEyMi4wMDEgQyA1OC4yNTQgMTIyLjA0NSAyNi4wNDUgMTU0LjI1NCAyNiAxOTQuMDAxIEwgMjYgMzg2LjAwMSBDIDI2LjA0NSA0MjUuNzQ3IDU4LjI1NCA0NTcuOTU5IDk4IDQ1Ny45OTkgTCA0MDIgNDU3Ljk5OSBDIDQ0MS43NDcgNDU3Ljk1OSA0NzMuOTU2IDQyNS43NDcgNDc0IDM4Ni4wMDEgTCA0NzQgMTk0LjAwMSBDIDQ3My45NTYgMTU0LjI1NCA0NDEuNzQ3IDEyMi4wNDUgNDAyIDEyMi4wMDEgTCAyMzkuNzQgMTIyLjAwMSBMIDE0OC4yNSA0NS43MTEgQyAxNDUuMzcxIDQzLjMxNiAxNDEuNzQ1IDQyLjAwNCAxMzggNDIuMDAxIFoiIHN0eWxlPSJ0cmFuc2Zvcm0tb3JpZ2luOiAyNTBweCAyNTBweDsiIHRyYW5zZm9ybT0ibWF0cml4KC0xLCAwLCAwLCAtMSwgLTAuMDAwMDE3LCAtMC4wMDAwMTcpIi8+Cjwvc3ZnPg==');
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.1;
}

/*---------- 自定义图标样式 -------- */
img[src="https://i.imgur.com/mwvyHE6.png"],
img[src="https://i.imgur.com/RyMc2aI.png"],
img[src="https://i.imgur.com/tBQN9h9.png"] {
    height: 48px !important;
    border-radius: 5px;
}



/*---- ---- ---- ---- ---- --- ---- ---- ---1.主页---- ---- --- ---- ---- --- ---- ---- ---- -------*/
/* 登录 */
#ls_fastloginfield_ctrl {
  background-color: var(--primary-color) !important;
  color: #fff !important;
  vertical-align: middle !important;
  display: flex !important;
  align-items: center;
  border: 1px solid var(--primary-color) !important;
  justify-content: center;
}

.pns .px {
border: 1px solid var(--primary-color) !important;
background-color: #fff !important;
margin-right: 10px;
width: 100px;
}

.rfm .px {
	width: 200px !important;
}

.fastlg_l {
padding-right: 4px !important;
border-right: 0px solid #E5EDF2;
}

.sltm {
padding: 5px 11px 5px 10px;
border: 1px solid #DDD;
background-color: #FFF;
text-align: left;
box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
}

.fastlg_l,
.psw_w {
color: var(--primary-font-color)!important;
}

.tbmu a {
color: var(--primary-font-color);
}

#threadlist > div.th > table > tbody:nth-child(1) > tr > th > a.a,
#threadlist > div.th > table > tbody:nth-child(1) > tr > th > a:nth-child(3){
color: #fff;
}

#thread_types > li:nth-child(13) > a > font,
#thread_types > li:nth-child(2) > a > b > font{
color: #f99d9b;
}

#thread_types > li:nth-child(13) > a > font:hover,
#thread_types > li:nth-child(2) > a > b > font:hover{
color: #f26c4f;
}

/* 头像 */
.avt img {
  width: 48px;
  height: 48px;
  border: 3px solid var(--primary-color);
  padding: 0;
  border-radius: 12px;
  margin: 0px 0px 0px 10px;
}

#uhd > div > div > a > img {
  margin: 0px 16px 0px 0px;
}
div > div.card_mn > div.avt > a > img {
  margin-left: 0px !important;
}
/* 文字 */
#um,
#um a {
color: var(--primary-font-color);
border: none;
}
#um, #um a:hover {
 background: none !important;
 border: none;
}
#pm_ntc.new {
	background-image: none !important;
	background: orange !important;
	border-radius: 6px !important;
	padding: 2px 4px;
}
#qmenu, .fl .bm_h h2 a, .fl .bm_h h2 span, #um .new, .topnav .new, .sch .new, .el .ec .hot em, .pll .s a.joins, #diy_backup_tip .xi2 {
color: var(--primary-font-color);
}
#um .new, .topnav .new, .sch .new, #toptb .new {
	padding-left: 0px !important;
}
.xi1, .onerror {
    color: red;
}
.ignore_notice {
background: none;
}
/* --- ---- pop---- ---- ---  */
#extcreditmenu_menu {
  width: 80px !important;
  color: var(--primary-font-color);
  margin-left: -9px !important;
  padding: 0px 10px 0px 10px;
}
#g_upmine_menu {
  width: 150px !important;
  margin-left: -9px !important;
  padding: 10px;
}
#myprompt_menu {
  width: auto;
  margin-left: -18px !important;
}
.p_pop a:hover, .p_pop a.a, #sctype_menu .sca {
background-color: transparent;
color: var(--primary-color);
}
#myitem_menu {
  margin-left: -20px !important;
}
.bbda {
border-bottom: none;
margin-top: 6px;
}
/* 积分 */
#extcreditmenu_menu li {
float: none;
display: block;
padding-left: 10px !important;
line-height: 2;
}
#extcreditmenu, #g_upmine {
margin-right: 1px !important;
padding-top: 3px;
padding-bottom: 3px;
/* padding-left: 10px; */
}
#extcreditmenu.a, #g_upmine.a {
position: relative;
z-index: 302;
border: none !important;
border-bottom: none !important;
background-color: transparent !important; /* 使用 transparent 替代 none */
}

/* 下拉框*/
.p_pop, .p_pof, .sllt {
background: rgba(240, 240, 243, 0.7) !important;
backdrop-filter: blur(30px) saturate(180%) !important;
border: 1px solid rgba(255, 255, 255, 0.7) !important;
box-shadow: 0 1px 50px rgba(0, 0, 0, 0.20), inset 0px 0px 50px rgba(255, 255, 255, 0.9) !important;
border-radius: 10px;
}

.p_pop a {
  border: none !important;
  text-align: center !important;
  white-space: nowrap;
  color: var(--primary-font-color);
}

/* --- ---- ---- ---- --- */
.pipe {
color: transparent !important;
}

#nv {
	overflow: hidden;
	margin: 15px 0;
	position: static !important;
	height: 50px;
	background: var(--list-color) !important;
	display: flex;
	align-items: center;
	border: 1px solid var(--list-border) !important;
	box-shadow: 0 1px 20px rgba(0, 0, 0, 0.05) !important;
}

#nv li.a {
  margin-left: -1px !important;
  background: none !important;
}

#nv li.a a,
#nv li a {
	color: var(--primary-font-color);
}

/* 搜索栏*/
#scbar {
	overflow: visible;
	background: none;
	border: none !important;
	margin-top: -62px;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 420px;
}

#scbar_type {
	width: 50px;
	height: 34px;
	padding-left: 0px;
	color: var(--primary-font-color) !important;
	margin: 10px;
	font-size: 13px;
	text-align: center;
	line-height: 34px;
}

#scbar_btn {
	margin: 0;
	padding: 0;
	border: none;
	position: relative;
	background: var(--primary-color) !important;
	box-shadow: none;
	margin-top: -2px;
}


#scbar_btn::after {
	content: "";
	position: absolute;
	width: 18px;
	height: 18px;
	background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDI0IDI0Ij48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Im0xMi41OTMgMjMuMjU4bC0uMDExLjAwMmwtLjA3MS4wMzVsLS4wMi4wMDRsLS4wMTQtLjAwNGwtLjA3MS0uMDM1cS0uMDE2LS4wMDUtLjAyNC4wMDVsLS4wMDQuMDFsLS4wMTcuNDI4bC4wMDUuMDJsLjAxLjAxM2wuMTA0LjA3NGwuMDE1LjAwNGwuMDEyLS4wMDRsLjEwNC0uMDc0bC4wMTItLjAxNmwuMDA0LS4wMTdsLS4wMTctLjQyN3EtLjAwNC0uMDE2LS4wMTctLjAxOG0uMjY1LS4xMTNsLS4wMTMuMDAybC0uMTg1LjA5M2wtLjAxLjAxbC0uMDAzLjAxMWwuMDE4LjQzbC4wMDUuMDEybC4wMDguMDA3bC4yMDEuMDkzcS4wMTkuMDA1LjAyOS0uMDA4bC4wMDQtLjAxNGwtLjAzNC0uNjE0cS0uMDA1LS4wMTgtLjAyLS4wMjJtLS43MTUuMDAyYS4wMi4wMiAwIDAgMC0uMDI3LjAwNmwtLjAwNi4wMTRsLS4wMzQuNjE0cS4wMDEuMDE4LjAxNy4wMjRsLjAxNS0uMDAybC4yMDEtLjA5M2wuMDEtLjAwOGwuMDA0LS4wMTFsLjAxNy0uNDNsLS4wMDMtLjAxMmwtLjAxLS4wMXoiLz48cGF0aCBmaWxsPSIjZmZmZmZmIiBkPSJNNSAxMGE1IDUgMCAxIDEgMTAgMGE1IDUgMCAwIDEtMTAgMG01LTdhNyA3IDAgMSAwIDQuMTkyIDEyLjYwNmw1LjEgNS4xMDFhMSAxIDAgMCAwIDEuNDE1LTEuNDE0bC01LjEtNS4xQTcgNyAwIDAgMCAxMCAzIi8+PC9nPjwvc3ZnPg==');
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

#scbar_btn:hover {
	box-shadow: none !important;
}

/* 透明背景*/
.scbar_txt_td,
.scbar_type_td {
background: none !important;
}
/* 删除多余的元素*/
.scbar_icon_td,
.__web-inspector-hide-shortcut__{
display: none !important;
}
/* 输入框*/
#scbar_txt {
	width: 400px;
	height: 30px;
	color: var(--primary-color) !important;
	background: var(--list-color2) !important;
	font-weight: 500;
	padding-left: 10px;
	transition: 200ms all 120ms ease-out;
	border: 1px solid var(--list-border);
}

#scbar_txt:focus {
	transform: scale(1.05);
	box-shadow: 0 0 50px var(--list-border) !important;
}
input::placeholder {
color: var(--primary-color);
opacity: 0.5;
}
/* 搜索按钮*/
.scbar_btn_td {
background: none !important;
}
#scbar_type_menu {
  margin-left: -7px;
  margin-top: 0px !important
}
#pt {
margin: 20px 0;
height: 29px;
border: none;
background: transparent;
line-height: 29px;
}
/* ————————今日———————————*/
.chart {
padding-left: 0px;
background: none !important;
color: var(--a-font-color) !important;
}
.chart em {
	color: var(--a-font-color);
}
#ancl > li,
.cl > .z.xw1:first-child {
display: none !important;
}
/*整体宽松*/
.temp {
margin: 1px;
padding: 5px;
}

/* ————————热门主题————————*/
#ct .frame {
  margin: 0;
  margin-bottom: 10px;
  background: none;
}
#framer9AF16_center .frame-tab .tb .a a {
  position: relative;
  margin-left: 16px;
  background-color: rgba(255, 255, 255) !important;
  cursor: pointer;
  color: var(--primary-color) !important;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.15);
  transition: .1s;
  border-radius: 8px!important;
  height: 27px;
}
#framer9AF16_center .frame-tab .tb .a a::before,#framer9AF16_center .frame-tab .tb .a a::after{
position: absolute;
bottom: 0;
content: '';
width: 20px;
height: 14px;
border-radius: 100%;
box-shadow: 0 0 0 40px #fff;
transition: .1s;
}
#framer9AF16_center .frame-tab .tb .a a::before{
left: -20px;
clip-path: inset(50% -10px 0 50%);
}
#framer9AF16_center .frame-tab .tb .a a::after{
right: -20px;
clip-path: inset(50% 50% 0 -10px);
}

.frame-tab .tb li, .frame-tab .tb li a {
  color: #fff;
}
/* 标签*/
.tb a {
display: block;
padding: 0 20px;
border: none !important;
  background-color: transparent;
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  font-size: 13px;
}

#diy_chart .tb .a a {
border-top: 0px solid #ba350f !important;
}
.index-top-frame .frame-title, .index-top-frame .frametitle, .index-top-frame .tab-title {
box-shadow: 0 1px 12px rgba(0, 0, 0, 0.1) !important;
background: #fff;
}
.index-top-frame .tab-title {
border-bottom: 0px solid var(--Second-color);
box-shadow: 0 0px 0px rgba(0, 0, 0, 0.0) !important;
}
.index-top-frame .frame-tab {
    margin-bottom: 0;
    border: none !important;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.03) !important;
    background: rgba(255, 255, 255);
}
/*调整内边距*/
.index-top-frame .frame-tab .tb-c {
  padding: 0 20px 2px;
}
#diy_chart .tb {
padding-left: 0;
padding-top: 5px;
}
.__web-inspector-hide-shortcut__ {
  box-shadow: 0 1px 12px rgba(0, 0, 0, 0.2) !important;
}
#ft {
padding: 30px 0 0;
border-top: none;
color: var(--primary-font-color);
}
#frt strong a,
#flk a {
color: var(--primary-font-color);
}
tr > th > span {
color: var(--a-font-color) !important;
}
.card_gender_1 {
  background: none;
}
#delform > table > tbody > tr.th {
  border-radius: 0 !important;
  background: transparent;
}
.fl .bm_c, #online .bm_c, .lk .bm_c {
	border-top-left-radius: 0;
	border-top-right-radius: 0;
}
.fl_row td {
	border-top: none  !important;
}
div#diy_chart {
	width: 918px !important;
}
.frame-2-1-l, .frame-1-2-r {
	width: 65% !important;
}
/*---- ---- ---- ---- ---- --- ---- ---- ---2.分区---- ---- --- ---- ---- --- ---- ---- ---- -------*/
/*子版块*/
#livethread {
	background: var(--list-color) !important;
	border: 1px solid var(--list-border) !important;
}
#livereplycontentout {
	border: none !important;
	background: rgba(0, 0, 0, 0.05) !important;
	padding: 5px;
}
#livereplycontent dl {
	border: none !important;
}
#livereplycontent {
	padding: 0 15px;
	width: 835px !important;
}
#livefastcomment {
  line-height: 20px;
  height: 20px !important;
  border: none;
  background-color: transparent;
}
.livethreadtitle a {
	color: red !important;
}
.fl .bm_h {
border: none !important;
background: none !important;
background-color: var(--primary-color) !important;
padding: 5px !important;
border-bottom-left-radius: 0 !important;  /* 默认移除底部圆角 */
border-bottom-right-radius: 0 !important; /* 默认移除底部圆角 */
}
#ct > div > div.bm.bmw.fl > div.bm_h.cl > h2 {
margin-left: 10px;
color: #fff !important;
font-size: 13px !important;
}
.fl {
border: 0px solid #CDCDCD !important;
border-top: none !important;
background: none !important;
}
.fl .bm {
	margin-bottom: 20px;
	overflow: hidden;
	background: var(--list-color2) !important;
	border: 1px solid var(--list-border) !important;
}
.bm_h .i {
  padding-left: 10px;
  color: var(--a-font-color) !important;
}

#thread_types > li:nth-child(14) > a > font,
#thread_types > li:nth-child(3) > a > b > font {
color: #f99d9b !important;
}

#um .showmenu {
	margin-right: 0;
	margin-right: 0
}
/* 原创 BT 电影*/
.fl .bm_h h2 span {
color: #fff !important;
padding: 10px;
font-size: 14px!important;
}
/* 屏蔽分区版主*/
.bmw .bm_h .y {
display: none;
}
/* 虚线*/
.fl_row td {
border-top: 1px dashed #f2f2f2;
}
/* 间距*/
.fl_tb td {
padding: 20px 0;
}
/*  列表高度 */
.tl th, .tl td {
padding: 10px 5px 10px 5px;
border-bottom: 0px solid #C2D5E3!important;
}
.tl .by {
width: 95px;
line-height: 1.2;
text-align: center;
}
.tl .num {
width: 59px;
line-height: 14px;
text-align: center;
}

.tl .icn {
width: 25px;
padding: 10px 0px !important;
}

.xst {
	font-size: 14px;
	text-align: center !important;
	color: var(--a-font-color) !important;
}

.tf a.xi2, .showmenu.xi2, .tl .th td, .tl .th th {
color: #fff !important;
font-size: 13px !important;
}

.cttp .unfold, .cttp .fold {
  color: var(--primary-color);
}

.ntc_l.hm.xi2 {
	border-radius: var(--icon-radius) !important;
	background: #ffe9bf !important;
	border: 1px solid var(--Second-color);
}

#ct > div > div.bm.bml.pbn > div.bm_h.cl > h1 > a {
  color: var(--a-font-color);
}

/*---- ---- ---- ---- ---- --- ---- ---- ---3.内页---- ---- --- ---- ---- --- ---- ---- ---- -------*/
/* 两边不遮住主体阴影 */
.mn {
overflow: visible;
}
/* 主体阴影 */
.bmw {
border: 0px solid #CDCDCD;
background-color: var(--list-color) !important;
box-shadow: 0 1px 10px rgba(0, 0, 0, 0.03) !important;
}

#nv_forum #ct {
	background: var(--list-color) !important;
	padding: 20px;
	border: 1px solid var(--list-border) !important;
	width: 918px !important;
  margin: 10px 0;
}

/* 综合讨论区 */
.bm.bml.pbn {
	padding: 10px !important;
	background: var(--list-color2) !important;
	box-shadow: none !important;
	border: 1px solid var(--list-border) !important;
}
.bm.bml.pbn * {
	font-size: 13px !important;
	color: var(--a-font-color) !important;
}
#ct > div > div.bm.bml.pbn > div.bm_c.cl.pbn > div,
#ct > div > div.bm.bml.pbn > div.bm_h.cl > h1{
text-align: left !important;
}
.ico_increase {
background: transparent url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgd2lkdGg9IjE4IgogICBoZWlnaHQ9IjE4IgogICB2aWV3Qm94PSIwIDAgMjQgMjQiCiAgIHZlcnNpb249IjEuMSIKICAgaWQ9InN2ZzEiCiAgIHNvZGlwb2RpOmRvY25hbWU9IkJ4c1Vwdm90ZS5zdmciCiAgIGlua3NjYXBlOnZlcnNpb249IjEuNCAoZTdjM2ZlYjEsIDIwMjQtMTAtMDkpIgogICB4bWxuczppbmtzY2FwZT0iaHR0cDovL3d3dy5pbmtzY2FwZS5vcmcvbmFtZXNwYWNlcy9pbmtzY2FwZSIKICAgeG1sbnM6c29kaXBvZGk9Imh0dHA6Ly9zb2RpcG9kaS5zb3VyY2Vmb3JnZS5uZXQvRFREL3NvZGlwb2RpLTAuZHRkIgogICB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxkZWZzCiAgICAgaWQ9ImRlZnMxIiAvPgogIDxzb2RpcG9kaTpuYW1lZHZpZXcKICAgICBpZD0ibmFtZWR2aWV3MSIKICAgICBwYWdlY29sb3I9IiNmZmZmZmYiCiAgICAgYm9yZGVyY29sb3I9IiMwMDAwMDAiCiAgICAgYm9yZGVyb3BhY2l0eT0iMC4yNSIKICAgICBpbmtzY2FwZTpzaG93cGFnZXNoYWRvdz0iMiIKICAgICBpbmtzY2FwZTpwYWdlb3BhY2l0eT0iMC4wIgogICAgIGlua3NjYXBlOnBhZ2VjaGVja2VyYm9hcmQ9IjAiCiAgICAgaW5rc2NhcGU6ZGVza2NvbG9yPSIjZDFkMWQxIgogICAgIGlua3NjYXBlOnpvb209IjU2LjA1NTU1NiIKICAgICBpbmtzY2FwZTpjeD0iOSIKICAgICBpbmtzY2FwZTpjeT0iOSIKICAgICBpbmtzY2FwZTp3aW5kb3ctd2lkdGg9IjEyMDAiCiAgICAgaW5rc2NhcGU6d2luZG93LWhlaWdodD0iMTE4OCIKICAgICBpbmtzY2FwZTp3aW5kb3cteD0iMjg2OSIKICAgICBpbmtzY2FwZTp3aW5kb3cteT0iODgiCiAgICAgaW5rc2NhcGU6d2luZG93LW1heGltaXplZD0iMCIKICAgICBpbmtzY2FwZTpjdXJyZW50LWxheWVyPSJzdmcxIiAvPgogIDxwYXRoCiAgICAgZmlsbD0iI2UxMWQ0OCIKICAgICBkPSJNNCAxNGg0djdhMSAxIDAgMCAwIDEgMWg2YTEgMSAwIDAgMCAxLTF2LTdoNGExLjAwMSAxLjAwMSAwIDAgMCAuNzgxLTEuNjI1bC04LTEwYy0uMzgxLS40NzUtMS4xODEtLjQ3NS0xLjU2MiAwbC04IDEwQTEuMDAxIDEuMDAxIDAgMCAwIDQgMTQiCiAgICAgaWQ9InBhdGgxIgogICAgIHN0eWxlPSJmaWxsOiNmZTAwMzg7ZmlsbC1vcGFjaXR5OjEiIC8+Cjwvc3ZnPgo=) no-repeat center center;
background-size: 14px;
background-position: 0px 0px !important;
}
.ico_fall {
background: transparent url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgd2lkdGg9IjE4IgogICBoZWlnaHQ9IjE4IgogICB2aWV3Qm94PSIwIDAgMjQgMjQiCiAgIHZlcnNpb249IjEuMSIKICAgaWQ9InN2ZzEiCiAgIHNvZGlwb2RpOmRvY25hbWU9IkJ4c0Rvd252b3RlLnN2ZyIKICAgaW5rc2NhcGU6dmVyc2lvbj0iMS40IChlN2MzZmViMSwgMjAyNC0xMC0wOSkiCiAgIHhtbG5zOmlua3NjYXBlPSJodHRwOi8vd3d3Lmlua3NjYXBlLm9yZy9uYW1lc3BhY2VzL2lua3NjYXBlIgogICB4bWxuczpzb2RpcG9kaT0iaHR0cDovL3NvZGlwb2RpLnNvdXJjZWZvcmdlLm5ldC9EVEQvc29kaXBvZGktMC5kdGQiCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgeG1sbnM6c3ZnPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGRlZnMKICAgICBpZD0iZGVmczEiIC8+CiAgPHNvZGlwb2RpOm5hbWVkdmlldwogICAgIGlkPSJuYW1lZHZpZXcxIgogICAgIHBhZ2Vjb2xvcj0iI2ZmZmZmZiIKICAgICBib3JkZXJjb2xvcj0iIzAwMDAwMCIKICAgICBib3JkZXJvcGFjaXR5PSIwLjI1IgogICAgIGlua3NjYXBlOnNob3dwYWdlc2hhZG93PSIyIgogICAgIGlua3NjYXBlOnBhZ2VvcGFjaXR5PSIwLjAiCiAgICAgaW5rc2NhcGU6cGFnZWNoZWNrZXJib2FyZD0iMCIKICAgICBpbmtzY2FwZTpkZXNrY29sb3I9IiNkMWQxZDEiCiAgICAgaW5rc2NhcGU6em9vbT0iNTYuMDU1NTU2IgogICAgIGlua3NjYXBlOmN4PSI5IgogICAgIGlua3NjYXBlOmN5PSI5IgogICAgIGlua3NjYXBlOndpbmRvdy13aWR0aD0iMTIwMCIKICAgICBpbmtzY2FwZTp3aW5kb3ctaGVpZ2h0PSIxMTg4IgogICAgIGlua3NjYXBlOndpbmRvdy14PSIzMDkzIgogICAgIGlua3NjYXBlOndpbmRvdy15PSI4MiIKICAgICBpbmtzY2FwZTp3aW5kb3ctbWF4aW1pemVkPSIwIgogICAgIGlua3NjYXBlOmN1cnJlbnQtbGF5ZXI9InN2ZzEiIC8+CiAgPHBhdGgKICAgICBmaWxsPSIjZTExZDQ4IgogICAgIGQ9Ik0yMC45MDEgMTAuNTY2QTEgMSAwIDAgMCAyMCAxMGgtNFYzYTEgMSAwIDAgMC0xLTFIOWExIDEgMCAwIDAtMSAxdjdINGExLjAwMSAxLjAwMSAwIDAgMC0uNzgxIDEuNjI1bDggMTBhMSAxIDAgMCAwIDEuNTYyIDBsOC0xMGMuMjQtLjMwMS4yODYtLjcxMi4xMi0xLjA1OSIKICAgICBpZD0icGF0aDEiCiAgICAgc3R5bGU9ImZpbGw6I2ZlMDAzOTtmaWxsLW9wYWNpdHk6MSIgLz4KPC9zdmc+Cg==) no-repeat center center;
background-size: 14px;
background-position: 0px 0px !important;
}

#pt .z em {
display: inline-block;
height: auto;
text-align: center;
line-height: 30px;
color: var(--primary-font-color);
font-size: 12px;
background: none;
vertical-align: middle;
overflow: hidden;
}
/*---- -- --- ---- ---- --- ---- ---- ---- -------*/
/* 发帖按钮 */
#newspecialtmp {
position: relative; /* 设置相对定位 */
display: inline-block;
width: 80px;
height: 40px;
margin-left: 5px !important;
background: none !important; /* 移除背景图 */
background-color: #f26c4f; /* 设置背景色 */
font-size: 14px; /* 字体大小 */
text-align: center; /* 文字水平居中 */
color: white; /* 字体颜色 */
line-height: 40px; /* 文字垂直居中 */
color: transparent; /* 确保文字暂时不可见 */
overflow: hidden; /* 隐藏超出部分 */
}
#newspecialtmp::before {
content: '发帖'; /* 添加文字内容 */
position: absolute;
top: 0;
left: 0;
width: 100%; /* 覆盖整个按钮 */
height: 100%;
background-color: var(--primary-color); /* 设置背景为红色 */
color: white; /* 设置文字颜色为白色 */
font-size: 16px; /* 设置文字大小 */
line-height: 40px; /* 文字垂直居中 */
font-weight: bold;
text-align: center;
letter-spacing: 3px;
z-index: 1; /* 确保文字覆盖图片 */
}

/* 回复按钮 */
#post_replytmp {
display: inline-block; /* 保持原有 inline-block 或 block 行为 */
width: 80px; /* 设置宽度 */
height: 40px; /* 设置高度 */
background: none; /* 移除背景图 */
font-size: 14px; /* 字体大小 */
color: white; /* 字体颜色 */
text-align: center; /* 水平居中 */
background-color: var(--primary-color); /* 设置背景色 */
position: relative; /* 为伪元素定位提供上下文 */
overflow: hidden; /* 避免多余内容溢出 */
}
#post_replytmp::before {
content: "回复"; /* 添加文字 */
position: absolute; /* 伪元素定位 */
top: 50%; /* 垂直居中 */
left: 50%; /* 水平居中 */
transform: translate(-50%, -50%); /* 修正偏移 */
font-size: 16px; /* 文字大小 */
color: white; /* 文字颜色 */
letter-spacing: 3px;
font-weight: bold; /* 文字加粗 */
pointer-events: none; /* 确保伪元素不影响交互 */
}
#post_replytmp:hover,
#newspecialtmp:hover {
text-shadow: 0 0px 5px rgba(255, 255, 255, 0.4);
}


/*---- -- --- ---- ---- --- ---- ---- ---- -------*/
/* 页数 */
.pg a, .pg strong, .pgb a, .pg label {
	background-color: var(--list-color);
	border-radius: 8px;
	border: 1px solid var(--list-border) !important;
}
.pg strong {
background-color: var(--primary-color);
color: #fff !important;
}
.pg a.nxt {
background: none !important;
background-color: var(--primary-color) !important;
align-items: center !important;
display: flex !important;
justify-content: center !important;
padding-right: 10px !important;
color: #fff !important;
}
.pgb a {
padding-left: 10px;
background: none !important;
background-color: var(--primary-color) !important;
color: #fff !important;
}
.pg label .px {
padding: 0;
width: 30px;
height: 16px;
border: none !important;
line-height: 16px;
border-radius: var(--icon-radius) !important;
text-align: center;
background: transparent !important;
}

/*---- -- --- ---- ---- --- ---- ---- ---- -------*/

.ttp .a a {
	padding-right: 12px;
	padding-left: 12px;
	color: #fff !important;
	/* border: none !important; */
	background-color: var(--primary-color) !important;
	border: 1px solid rgba(255, 255, 255, 0.7) !important;
	border-radius: 8px !important;
}
.ttp .a .num {
	background: transparent;
	color: #fff !important;
}
.ttp a, .ttp strong {
	float: left;
	color: var(--tag);
	padding: 2px 8px;
	height: 18px;
	background: var(--list-color);
	margin: 0 15px 5px 0;
	border: 1px solid var(--list-border);
	border-radius: 8px !important;
}
.ttp a:hover {
  border-color: var(--hover-border);
}
.ttp .num {
	background: none;
	padding: 0;
	border-radius: var(--icon-radius) !important;
	color: var(--tag) !important;
	margin-left: 5px;
  opacity: 0.7;
}
.tl .th {
margin-top: 1px;
padding: 0 10px;
border-bottom-left-radius: 0 !important;
border-bottom: 0px solid #b3ab9c;
background: var(--primary-color);
border-bottom-right-radius: 0 !important;
}

#thread_types > li:nth-child(3) > a:nth-child(1) > b:nth-child(1) > font:nth-child(1),
#thread_types > li:nth-child(7) > a:nth-child(1) > font:nth-child(1) {
color: var(--primary-color) !important;
}
.tf a.xi2,
.showmenu.xi2,
.tl .th td, .tl .th th {
color: #fff !important;
}

/* ------主体------- */
#pgt .pg, #pgt .pgb {
margin-top: 0px;
}
.tl #forumnewshow a {
display: block;
border-top: 0px solid #F4E4B4;
border-bottom: 0px solid #F4E4B4;
text-indent: 25px;
height: 35px;
line-height: 35px;
background-color: #ffe5b8;
color: #f26c4f;
justify-content: center;
margin-top: -10px;
}
.tl #forumnewshow {
background: #fff;
font-size: 12px;
text-align: center;
}
#forumnewshow > tr > td {
background: none !important;
}
.closeprev {
display: none !important;
}
.tl th a:visited {
color: #888;
}
.card_gender_0 {
  background: none;
}
.bui .m img {
margin-bottom: 0px !important;
width: 120px;
outline: 4px solid var(--primary-color);
}
.card .o {
  padding: 0px;
  margin: 5px 16px 0px 12px;
}
.card .o a {
  float: left;
  padding: 5px 2px;
  color: #fff;
  border: none;
  background: none;
  background-color: var(--primary-color) !important;
  text-align: center;
}
.card_mn, .card_info {
  padding: 14px 20px 10px 80px;
}
.bui .i {
  display: flex !important;
  font-size: 13px !important;
  flex-direction: column;
  align-items: flex-start;
}
.bui dl {
  margin-left: -16px !important;
}
.pls dd {
  width: 70px;
  margin-left: -30px !important;
}
/* 名字 */
.pls .pi {
padding: 8px;
text-align: center;
color: var(--primary-font-color) !important;
font-size: 14px !important;
margin: 5px 0;
}

.avatar {
width: 100px; /* 父容器宽度 */
}

.pbg2,
.pls .o .pm2 {
display: none !important;
}

.ad .pls {
background: #369;
height: 1px;
}
.pi {
overflow: hidden;
margin-bottom: 10px;
padding: 10px 0;
height: 16px;
border-bottom: none;
margin-top: 5px;
}
.ad td.pls {
background-color: var(--Second-color) !important;
border-radius: 0 !important;
}
.pls .tns {
padding: 10px 20px;
color: var(--primary-font-color);
}
.pm_c {
  padding-left: 40px;
  line-height: 22px;
}
.pl .blockcode {
  border: 1px solid #ffe6b4 !important;
  background: none !important;
  background-color: #fff4de !important;
  color: #97694d;
}
.pl .blockcode ol li:hover {
  background: #fff4de !important;
  color: var(--primary-color)!important;
  border-radius: 4px!important;
  width: 698px;
}
.pl .blockcode em {
  margin-left: 43px;
  color: var(--primary-font-color) !important;
  font-size: 12px;
}
/* 图片 */
.zoom {
display: block;
margin: 30px auto !important;
outline: 6px solid rgba(0, 0, 0, 0.1);
border-radius: 14px;
}
[id^="attach_"] {
white-space: normal !important;
}
.tip {
color: var(--a-font-color);
padding: 10px;
text-align: left;
background: rgba(255, 255, 255, 0.7);
border-radius: 8px;
box-shadow: 0 1px 30px rgba(0, 0, 0, 0.20), inset 0px 0px 30px rgba(255, 255, 255, 0.9);
backdrop-filter: blur(30px) saturate(180%) !important;
border: 1px solid rgba(255, 255, 255, 0.7) !important;

}
.zoominner p a {
  border-radius: 0 !important;
}
/* 为箭头创建一个模糊效果，并居中 */
.tip::after {
  content: ''; /* 插入箭头 */
  position: absolute;
  bottom: -12px; /* 将箭头放在弹窗下边缘外 */
  left: 50%; /* 将箭头放在弹窗的水平中间 */
  transform: translateX(-50%); /* 将箭头完全居中 */
  border-width: 6px; /* 箭头大小 */
  border-style: solid;
  border-color: #ffff transparent transparent transparent; /* 下箭头颜色与背景一致 */
}
#imgzoom_zoom {
box-shadow: 0 1px 10px rgba(0, 0, 0, 0.15) !important;
}
/* 按钮 */
.flbc {
  float: left;
  width: 18px;
  height: 18px;
  overflow: hidden;
  text-indent: -9999px;
  background: #000;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  position: relative;
  transition: background 0.15s ease;
}
.flbc:hover {
  background: red;
}
.flbc::before,
.flbc::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px; /* 将"X"的线条长度调整为10px */
  height: 2px; /* "X"的线条粗细 */
  background: white; /* 白色线条 */
  transform: translate(-50%, -50%) rotate(45deg); /* 旋转45度 */
  transition: background 0.15s ease; /* 添加过渡效果 */
}

.flbc::after {
  transform: translate(-50%, -50%) rotate(-45deg); /* 旋转-45度 */
}

.t_l, .t_c, .t_r, .m_l, .m_r, .b_l, .b_c, .b_r {
overflow: hidden;
background: none;
opacity: 0.2;
filter: alpha(opacity=20);
}
/* 按钮 */
.pn, .tb .o, .tb .o a {
background-image: none;
}
.pn {
  vertical-align: middle;
  overflow: hidden;
  margin-right: 3px;
  color: #fff !important;
  border-radius: var(--icon-radius) !important;
  padding: 0;
  height: 25px;
  border: none;
  background-color: #000 !important;
      -webkit-box-shadow: none  !important;
}
.pn.pnc:hover {
	box-shadow: 0 0 15px rgba(0, 0, 0, 0.3) !important;
	transition: background 0.15s ease;
}
.pns .pn {
  height: 25px !important;
  width: auto !important;
}
.pi strong a:hover {
  border-color:transparent;
  color: var(--primary-font-color);
}
.pi strong a {
	border: none  !important;
}
/* 回帖 */
.authi > .pipe,
.authicn.vm {
display: none;
}
/* 发表于 */
.pi em {
color: var(--primary-font-color) !important;
}
/* 来自手机 */
.xg1, .xg1 a {
color: var(--primary-font-color) !important;
}
.pbw p {
  color: var(--primary-font-color) !important;
  margin-bottom: 10px;
}
/* 只看该作者 */
.authi a {
color: var(--primary-font-color) !important;
}

/* 楼主签名 */
.ratl th, .ratl td, .ratc {
padding: 10px 15px;
border-bottom: none;
border-radius: 0px !important;
}
.ratl .xw1 .xi1 {
background: transparent;
}
.m_c .o {
display: flex;
justify-content: flex-end;
align-items: center;
gap: 10px;
padding: 10px 20px;
height: 26px;
border: none !important;
background: none;
}
.f_c .list {
	border-top: 1px solid rgba(0, 0, 0, 0.10);
	border-radius: 0 !important;
}
.t_l, .t_r, .b_l, .b_r {
display: none !important;
}
/* 购买附件 */
.flb em {
float: left;
font-size: 14px;
font-weight: 700;
color: var(--a-font-color);
}
#pid26441264 > tbody > tr:nth-child(1) > td.plc > div.pct > div > div.t_fsz > div > ignore_js_op:nth-child(4) > dl > dd {
margin-left: 80px;
color: var(--primary-font-color);
}
.tattl dd {
  margin-left: 0px;
  color: var(--a-font-color);
  overflow: hidden;
  padding: 0 4px;
}
.tattl dt {
  padding: 0!important;
  margin-right: 5px;
  width: 48px;
  height: 48px;
  line-height: 48px;
}
.tattl dt img {
  width: 48px;
}
.tattl strong, .tattl a {
  color: #f44646;
}
/* 作者 */
.f_c a {
color: var(--a-font-color);
}
.dd > p {
width: 100px !important;
color: #fff !important;
}
.xg2 {
color: var(--a-font-color);
}
.viewpay {
  padding: 0;
  background: none;
}
.locked a, .attach_nopermission a {
  color: #444;
}
.pcb .vm {
	height: 48px;
	width: 48px;
}
/* ---购买记录--- */
#fwin_pay {
position: fixed;
z-index: 201;
left: 315px;
top: 578px;
box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2) !important;
background-color: rgb(255, 255, 255) !important;
}
.m_c {
background: none;
}
.f_c .list th, .f_c .list td {
	padding: 11px 2px;
	height: auto;
	border-bottom: 1px dashed rgba(0, 0, 0, 0.10);
	width: 100px;
	border-radius: 0 !important;
}
/* 查看评分 */
#fwin_viewratings {
	backdrop-filter: blur(30px) saturate(180%) !important;
	border: 1px solid rgba(255, 255, 255, 0.7) !important;
	box-shadow: 0 1px 50px rgba(0, 0, 0, 0.20), inset 0px 0px 90px 30px rgba(255, 255, 255, 0.7) !important;
	background: rgba(240, 242, 246, 0.85) !important;
}
.po .y {
	margin: 15px 0 0 5px;
}
/* 回复 */
#fwin_reply {
  border-radius: 12px;
}
#fwin_content_reply {
background-color: transparent !important;
width: 500px;
}
#moreconf {
/* background: #fff; */
background-color: transparent !important;
}
.tedt .bar {
padding: 0 10px 0 0;
height: 25px;
line-height: 25px;
border-bottom: 1px solid var(--Second-color);
background: var(--tedt-bar-color);
border-bottom-left-radius: 0;
border-bottom-right-radius: 0;
}
.tedt .area {
padding: 4px;
zoom: 1;
background-color: transparent;
}
.tedt {
background-color: rgba(255, 255, 255, 0.5);
border: 1px solid var(--Second-color) !important;
}
.t_f a {
color: #f26c4f;
}
.quote blockquote, .pl .quote blockquote, .pl .quote {
  background: none !important;
}
.tedt .pt:focus,
#pmform > div > div.area {
  background: #FFF;
  border-radius: 0 0 6px 6px !important;
}
.z.noise {
  padding-top: 15px;
}

.tedt .pt {
  background: transparent !important;
}

.po {
border: none !important;
}
.flb span a:hover {
color: #f26c4f!important;
}
.tns th {
border-right: 0px solid #CCC;
}
.pls p, .pls .o {
margin: 0px 10px 5px 10px;
text-align: center;
}
.pil.cl {
  text-align: center;
}
.pls dt {
  margin-left: 16px;
  color: var(--primary-font-color);
  margin-right: 0px;
  width: 55px;
  font-size: 12px;
}

.xi2, .xi2 a {
color: var(--a-font-color);
}
#v_threads li, #v_forums li {
padding-left: 20px;
height: 20px;
background: url(/static/image/common/dot.gif) no-repeat 0 7px;
}
#visitedforums_menu a {
white-space: normal !important;
display: inline-block;
overflow: hidden;
height: 2.5em;
}
.pl .blockcode {
padding: 10px 0 5px 10px;
border: 1px solid #e5e5e5;
background: #f7f7f7 url(/static/image/common/codebg.gif) repeat-y 0 0;
overflow: hidden;
}
.icon_ring {
background: #FFF4DD;
display: inline-block;
width: 8px !important;
height: 8px !important;
margin-right: 5px;
border: 2px solid #F26C4F;
-webkit-border-radius: 10px;
-moz-border-radius: 10px;
border-radius: 10px;
box-shadow: 0px 0px 1px rgba(0,0,0,0.2);
overflow: hidden;
}
div.i.y > dl > dd > a {
  margin-left: 10px;
}
.ratl img {
  border-radius: 50%;
}
#postimg_param_3,
#postimg_param_2,
#postimg_param_1,
#posturl_param_2,
#posturl_param_1,
#postcode_param_1,
#postquote_param_1 {
  border: 1px solid var(--Second-color);
}
.psth {
    background: none !important;
}
/* ————————————————————搜索————————————————————*/
/* 搜索过于频繁，请10秒后再试*/
.nfl .f_c {
    margin: 60px auto;
    padding: 20px;
    width: 580px;
    background: rgba(240, 242, 246, 0.6);
    align-items: center !important;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.8) !important;
    box-shadow: 0 1px 50px rgba(0, 0, 0, 0.10), inset 0px 0px 50px 10px rgba(255, 255, 255, 0.2)  !important;
}
.alert_right, .alert_error, .alert_info {
  padding: 0;
  min-height: 40px;
  line-height: 40px;
  font-size: 22px;
  font-weight: 500;
  color: var(--a-font-color);
  text-align: center;
  background: transparent;
}
.alert_error {
  color: red;
}
.pbw {
	padding-bottom: 20px !important;
	background: rgba(255, 255, 255, 0.8) !important;
	padding: 20px;
	border: 1px solid rgba(255, 255, 255, 0.8) !important;
}

#threadlist .pbw {
	margin:15px 0 !important;
}

#messagelogin {
    border-top: none;
}

h3.xs3 a {
color: #4f4945 !important;
font-weight: 500;
}

.slst {
	width: auto !important;
	max-width: 1000px !important;
}
.slst p span a, .slst p span a:visited {
color: var(--primary-font-color) !important;
}
.slst p span {
color: var(--primary-font-color) !important;
}
#scform_tb a {
  margin: 0 15px 0 13px;
  color: var(--primary-font-color) !important;
}
#scform_tb > span > a:nth-child(2) {
  margin-left: 6px;
  margin-right: 32px;
}
#scform_tb > a:nth-child(3) {
  margin: 0 0 0 -5px;
}
#srchfid > option {
  background-color: transparent !important;
}
.pg a:hover, .pgb a:hover {
  color: var(--primary-color);
}
#scrolltop {
	bottom: 125px !important;
	width: 30px !important;
	background: rgba(255, 255, 255, 0.8) !important;
	border-radius: 8px !important;
	margin-left: 30px !important;
	border: 1px solid rgba(255, 255, 255, 0.8) !important;
}
#scrolltop a {
  width: 24px;
  height: 24px;
  padding: 3px 5px;
  border-top: none;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjQgMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgyNHYyNEgwVjBaIi8+PHBhdGggZD0iTTEwIDE2aDRjLjU1IDAgMS0uNDUgMS0xdi01aDEuNTljLjg5IDAgMS4zNC0xLjA4LjcxLTEuNzFMMTIuNzEgMy43Yy0uMzktLjM5LTEuMDItLjM5LTEuNDEtLjAwMWwtNC41OSA0LjU5Yy0uNjMuNjMtLjE5IDEuNzEuNyAxLjcxSDl2NWMwIC41NS40NSAxIDEgMVptLTQgMmgxMmMuNTUgMCAxIC40NSAxIDFzLS40NSAxLTEgMUg2Yy0uNTUgMC0xLS40NS0xLTFzLjQ1LTEgMS0xWiIvPjwvc3ZnPg==');
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.3;
}
a.scrolltopa:hover {
  background-position: 0px !important;
}
/* ———————————————————————————————————————设置—————————————————————————————————————————*/
/* ————————————左————————————————*/
#scform_srchtxt {
	padding: 0 10px;
	outline: none;
	width: 430px;
	color: var(--primary-color) !important;
	font-weight: 500 !important;
	background: rgba(255, 255, 255, 0.8);
	border: 1px solid rgba(255, 255, 255, 0.8) !important;
}
.sttl {
margin: 10px 0;
padding: 10px;
border: none !important;
background: var(--primary-color);
color: #fff;
}
#scform_submit {
width: 70px;
opacity: 1;
filter: alpha(opacity=0);
border: 1px solid var(--primary-color) !important;
background: var(--primary-color);
color: #fff;
box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
font-size: 15px;
}
.td_srchtxt,
.td_srchbtn,
#scform_form {
background: none!important;
}
#scform {
	margin: 10px auto;
}
#scform_form {
background: none!important;
empty-cells: show;
border-collapse: separate;
padding-right: 20px;
border-spacing: 10px 0;
margin-left: -30px;
}
.xs0 {
  font-size: 12px;
  line-height: 1.8;
  padding: 5px;
}
.tb {
  margin-top: 10px;
  padding-left: 5px;
  line-height: 30px;
  border-bottom: 1px solid var(--sider-color);
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.tb a:hover {
  background: transparent  !important;
  color: red;

}

.tb .o, .tb .o a {
  height: 23px;
  line-height: 23px;
  background: none;
  color: var(--a-font-color);
  border: none !important;
}
.tb .o a {
  padding: 3px 15px;
}
.tbn {
  display: flex !important;
  margin: 0px 0px;
  text-align: center;
  flex-direction: column;
  align-items: center;
}
.tbn li {
  margin: 0;
  height: 33px;
  border: none;
}
.tbn ul a {
  display: block;
  width: 100px;
  color: #fff;
}
.tbn li.a {
  border: none;
  margin: 0;
  padding: 0;
  background: none;
}
.tbn .mt {
  color: #fff;
  font-size: 16px;
  padding: 10px;
}
.xlda dd a {
  color: #f26c4f;
}
.pm_o .p_pop {
  text-align: left;
  margin-left: 31px;
  margin-top: 5px;
}
.notice_pm {
  background-image: none;
  margin-left: 14px !important;
}
.appl {
  float: left;
  overflow: hidden;
  padding: 0px 0px;
  margin-bottom: 0px;
  line-height: 2.5;
}
/* ————————————右————————————————*/
.ct2_a .mn {
	display: inline;
	margin-right: 0px;
	width: 803px;
	min-height: 350px;
	background-color: rgba(255, 255, 255, 0.9);
	border-left: 0px solid var(--Second-color);
	padding: 10px;
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
}
.dt {
  border-top: 0px solid #CDCDCD;
  width: 100%;
}
.dt th {
  background: var(--sider-color);
}
tr:nth-child(1) > th:nth-child(1) {
  border-radius: var(--icon-radius) 0 0 6px;
}
tr:nth-child(1) > th:nth-child(4) {
  border-radius: 0 6px 6px 0;
}
.dt td, .dt th {
  padding: 7px 4px;
  border-bottom: 0px dashed #e5e5e5;
  border-radius: 0;
}
.px, .pt, .ps, select {
	border: 1px solid var(--primary-color);
	background: #fff !important;
	border-radius: var(--icon-radius);
	padding: 2px 6px;
}
.px, .pt {
	padding: 4px;
}
.ps, select {
padding: 2px 2px 2px 6px #fff !important;
}
#e_textarea {
  background-color: #ffffff !important;

}
.exfm {
  border: 1px solid var(--Second-color);
  background: var(--sider-color) !important;
}
.pml dl {
  padding-left: 65px;
  background-color: var(--sider-color) !important;
  margin-left: 16px !important;
  margin-right: 16px !important;
  border: 1px solid var(--Second-color) !important;
}
.tf a.xi2, .showmenu.xi2, .tl .th td, .tl .th th {
  color: #fff !important;
}
.tfm .d {
  clear: both;
  margin: 5px 0;
  color: var(--primary-font-color);
  text-align: left;
}
#selBox {
  border: solid #fff;
}
.pmform .px {
  width: 400px;
   text-align: left;
}
.pgs.pbm.cl.pm_op {
  margin-left: 18px !important;
}
.un_selector input {
  width: 390px;
  height: 21px;
  outline: none;
  border: 0px solid var(--Second-color) !important;
  background-color: #fff !important;
}
.pmform .tedt {
  width: 600px;
}
.tb .a a, .tb .current a {
  border-bottom-color: #FFF;
  background: none;
  background: transparent;
  color: var(--primary-color);
  font-weight: 700;
}
#showSelectBox {
color: var(--primary-font-color) !important;
}
.nts dl {
  border-bottom: 1px dashed #e5e5e5;
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.nts {
  padding-left: 10px;
}
.xld dd {
  margin-bottom: 8px;
  margin-left: 80px;
}
dt > span {
  margin-left: 16px !important;
}
.pgs {
  padding: 5px 0 5px 0;
  margin-left: -6px;
  margin-top: 20px;
}
.tbmu {
  padding: 0;
  border-bottom: 0px dashed #CDCDCD;
}
.bmw .bm_h {
	color: #fff;
	border-radius: 8px !important;
	margin: 10px;
}
#f_pst {
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2) !important;
  overflow: hidden;
}
.bm_h {
  padding: 0 10px;
  height: 31px;
  border-top: 0px solid #FFF;
  background: var(--primary-color);
  line-height: 31px;
  border-radius: 0;
  white-space: nowrap;
  color: var(--primary-color);
  overflow: hidden;
}
#f_pst .px {
  padding: 4px;
  background-color: var(--sider-color) !important;
  border: 1px solid var(--Second-color) !important;
}
.ftid a {
  display: block;
  overflow: hidden;
  padding: 4px;
  height: 17px;
  border-radius: var(--icon-radius) !important;
  line-height: 17px;
  font-size: 12px;
  font-weight: 400;
  text-align: center !important;
  color: var(--primary-color) !important;
  background: none;
  border: 1px solid var(--Second-color) !important;
  background-color: #ffffff !important;
}
.pl.bm {
  border: 0px solid var(--Second-color);
  background: transparent;
}
.plhin {
    overflow: hidden;
    background-image: none !important;
}
table {
  background-color: transparent;
}
.pls {
  width: 160px;
  overflow: hidden;
  border-right: var(--sider-line);
  border-radius: 0;
  background: none !important;
}
.plc {
  padding: 0 20px;
  border-radius: 0;
  background: var(--postlist-color)  !important;
}
.quote blockquote,
.pl .quote blockquote,
.pl .quote {
  background:none !important;
  background-color: transparent !important;

}
.ptn {
  padding-top: 0px !important;
  vertical-align: middle !important;
  padding-bottom: 0px !important;
}
.pl table {
	margin-bottom: 10px;
	border-radius: 10px;
	background: rgba(255, 255, 255, 0.5);
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.03);
}
.dd > table, .tns.xg2 > table, .ratl, .rate, .t_fsz > table {
	border: none !important;
	box-shadow: none !important;
	background: transparent !important;
}
.rate {
background: rgba(240, 240, 243, 0.8);
border-radius: 14px;
}
.vwthd {
padding: 10px !important;
vertical-align: middle !important;
border-bottom-right-radius: 10px;
border-top-right-radius: 10px;
}
#p_btn a,
#p_btn i {
  background: none;
}
#p_btn i {
  background-color: #c570ba !important;
  color: #fff;
  padding: 4px 12px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.5s ease !important;
}
#p_btn i:hover {
  background-color: red !important;
}
#p_btn span {
  padding-left: 6px;
  color: #fff;
}
.bmn, .pg a, .pgb a, .pg strong, .card, .card .o, div.exfm {
  border-color: var(--Second-color);
}
.pm_o .o {
width: 17px;
height: 17px;
text-indent: 20px;
background: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0Ij4KICA8cGF0aCBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIi8+CiAgPHBhdGggZD0iTSAxMiAyIEMgNi40OCAyIDIgNi40OCAyIDEyIEMgMiAxNy41MiA2LjQ4IDIyIDEyIDIyIEMgMTcuNTIgMjIgMjIgMTcuNTIgMjIgMTIgQyAyMiA2LjQ4IDE3LjUyIDIgMTIgMiBaIE0gMTAgMTYuNSBMIDEwIDcuNSBMIDE2IDEyIEwgMTAgMTYuNSBaIiBzdHlsZT0idHJhbnNmb3JtLWJveDogZmlsbC1ib3g7IHRyYW5zZm9ybS1vcmlnaW46IDUwJSA1MCU7IGZpbGw6IHJnYigxNjgsIDg1LCA5NCk7IiB0cmFuc2Zvcm09Im1hdHJpeCgwLCAxLCAtMSwgMCwgMC4wMDAwMDEsIDApIi8+Cjwvc3ZnPg==') no-repeat;
background-position: 0px 1px !important;
}
.xlda dl {
	padding-left: 0;
}
.xld .m {
	margin: 8px 8px 10px 0;
}
.cl > dd > img {
	border: none;
}


/* ————————————个人资料————————————————*/
.ct1.wp.cl {
  background-color: #fff !important;
  border-radius: 0 0 8px 8px !important;
  border: 0px solid var(--primary-color);
  border-top: 0 !important;
  box-shadow: 0 1px 20px rgba(0, 0, 0, 0.1) !important;

}
#uhd .tb .a a {
  position: relative;
  margin-left: 16px;
  background-color: #fff !important;
  cursor: pointer;
  color: var(--primary-color) !important;
  transition: .2s;
  border-radius: 8px!important;
  height: 29px;
}
#uhd .tb .a a::before,#uhd .tb .a a::after {
position: absolute;
bottom: 0;
content: '';
width: 20px;
height: 14px;
border-radius: 100%;
box-shadow: 0 0 0 40px #fff;
transition: .2s;
}
#uhd .tb .a a::before {
left: -20px;
clip-path: inset(50% -10px 0 50%);
}
#uhd .tb .a a::after {
right: -20px;
clip-path: inset(50% 50% 0 -10px);
}
#uhd > ul > li:nth-child(1) > a::hover {
  color: var(--primary-color) !important;
}
#uhd {
  border: 0px solid var(--primary-color);
  border-bottom: none;
  background: var(--primary-color);
  border-radius: 8px 8px 0 0  !important;
  box-shadow: 0 1px 20px rgba(0, 0, 0, 0.1) !important;
}
.ct2_a, .ct3_a {
  background-image: none;
  border: 0px solid var(--Second-color);
  background: var(--primary-color);
  box-shadow: 0 1px 30px rgba(0, 0, 0, 0.1);
}
.tb {
  margin-top: 10px;
  padding-left: 5px;
  line-height: 30px;
  border-bottom: 0px solid var(--sider-color);
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.tb .a a, .tb a:hover {
  background: transparent !important;
  color: var(--primary-color) !important
  border: 0px solid var(--Second-color) !important;
  border-bottom: #fff !important;
}
#uhd > ul > li:nth-child(1) > a,
#uhd .mt {
  color: #fff;
}
#uhd > div > p > a {
  color: #fff!important;
}
#calendar_week td,
#calendar_header td {
  border-bottom: 0px solid #C2D5E3 !important;
}
#calendar {
  border: 0px solid #DDD!important;
  box-shadow: 0 1px 16px rgba(0, 0, 0, 0.15)!important;
}
.calendar_default, .calendar_default a:link, .calendar_default a:visited {
  color: var(--a-font-color)!important;
}
td.calendar_checked, span.calendar_checked {
  background: #ffffff!important;
}
td.calendar_checked, span.calendar_checked {
  background: #f26c4f!important;
}
.calendar_checked, .calendar_checked a:link, .calendar_checked a:visited {
  color: #ffffff!important;
}
#calendar_week a {
  color: var(--a-font-color)!important;
}
.pm_c .o {
  float: left;
  display: inline;
  margin: 5px 0 0 -25px;
  margin-top: 0;
}
.tfx .alt, .tfx .alt th, .tfx .alt td {
  background: var(--Second-color);
}
.tfx th, .tfx td {
  border-width: 0 3px !important;
  background: #fff;
}
.tdat th, .tdat td {
  padding: 4px 5px;
  border: 0px solid #CDCDCD;
  border-radius: 0 !important;
}
.tdat {
  border: 1px solid var(--Second-color);
}
.alt, .alt th, .alt td {
  background-color: var(--sider-color);
}
#normalthread_2514014 > tr > td:nth-child(3) > cite {
    color: #FF5733;
}
#uhd > ul > li:nth-child(1) > a {
     color: var(—background-color) !important;
}
#uhd > ul > li:nth-child(2) > a:hover {
     color: var(—primary-font-color) !important;
}
.tl .bm_c {
	background: rgba(255, 255, 255, 0.8) !important;
	border-top-left-radius: 0;
	border-top-right-radius: 0;
}
/* ————————————加为好友/发送消息————————————————*/
#uhd .mn .pm2 a,
#uhd .mn .addf a {
  background:none;
  color: #fff;
  margin-left: 16px;
}
#uhd .mn {
  margin-right: 33px;
  margin-top: 14px;
  line-height: 30px;
}
#uhd .mn .pm2 a, #uhd .mn .addf a {
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 5px 12px;
}
#uhd .tb a, #flw_header .bar {
  color: #fff;
}
.fwinmask {
	background: rgba(240, 242, 244, 0.85) !important;
	border: 1px solid rgba(255, 255, 255, 0.9) !important;
	box-shadow: 0 1px 50px rgba(0, 0, 0, 0.20), inset 0px 0px 60px 20px rgba(255, 255, 255, 0.5) !important;
	overflow: hidden;
  margin: 0 80px;
	backdrop-filter: blur(30px) saturate(180%) !important;
}
.pm .flb {
  background: var(--Second-color);
  padding: 10px 10px 8px;
}
.pm .c {
  padding: 0;
  background: transparent;
}
.pm .flb em {
  padding-left: 0px;
  background: none;
  text-shadow: none;
  color: var(--a-font-color);
  font-size: 13px;
}
.mtn {
  margin-top: 15px !important;
}
#pmform_532461 > div.mtn.pns.cl > div > a {
  margin-top: -10px;
  color: var(--a-font-color);
  display: block;
}
.pm .flbc {
  background-image: none;
}
.pm_tac {
  padding: 5px 10px;
  background: #cdcdcd6b;
}
.buddy .avt {
  position: absolute;
  margin-left: -70px;
}
p.mtm.cl.pns .z {
  color: #fff;
  line-height: 25px;
}
.avt.avtm img {
  height: 100px;
  width: 100px;
}
.tfm caption, .tfm th, .tfm td {
  vertical-align: top;
  padding: 7px 0;
  vertical-align: middle;
}
.bm.mtm>.bm_h {
  color: #fff;
  border: none;
}
.bm.mtm {
  border: 1px solid var(--primary-color);
  overflow: hidden;
}
.bm_h.cl a {
  color: var(--a-font-color);
}
.buddy_group li {
  overflow: hidden;
  padding: 5px 0;
  line-height: 20px;
  border-bottom: 1px dashed var(--Second-color);
  border-radius: 0;
}

/*---------- 瀑布流 -------- */
#frame8nZQJt {
  margin-bottom: 10px;
  border: 0px solid #CCC;
  background: #FFF;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
}
.frame-title, .frametitle, .tab-title {
  background: none;
}
.frame-title, .frametitle, .tab-title {
  background: none;
  background-color: var(--primary-color) !important;
  border-radius: 10px 10px 0 0;
  color: #fff;
}
.diy-m1 li {
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.15) !important;
}

/*---------- 小黑屋 -------- */
#darkroomtable {
  box-shadow: 0 1px 16px rgba(0, 0, 0, 0.1);
  text-align: center;
  font-size: 13px;
  line-height: 2.0;
  border-radius: 8px!important;
  overflow: hidden !important;
}
#darkroomtable > tbody > tr:nth-child(1) > th {
   border-radius: 0 !important;
   background-color: var(--primary-color) !important;
   color: #fff;
   font-size: 14px;
   text-align: center;
}
#darkroomtable > tbody > tr:nth-child(1) > th:nth-child(1){
   border-radius: var(--icon-radius) 0 0 0 !important;
   width: 130px !important;
}
#darkroomtable > tbody > tr:nth-child(1) > th:nth-child(5) {
   border-radius: 0 6px 0 0!important;
}
#darkroomtable .alt td {
   background-color: var(--td-color);

}
/*---------- 签到 -------- */
.ddpc_sign_table {
	color: var(--a-font-color) !important;
	font-size: 13px !important;
	border-radius: 8px !important;
	overflow: hidden !important;
	background: rgba(255, 255, 255, 0.7) !important;
	box-shadow: 0 1px 20px rgba(0, 0, 0, 0.05) !important;
}
.ddpc_sign_btn_red {
  background: #000 !important;
  border-radius: 8px !important;
  color: #fff !important;
}
.ddpc_sign_table table th {
  color: #fff !important;
  border-radius: 0 !important;
  background: var(--primary-color) !important;
}
.ddpc_sign_table table tr:nth-child(1) th:nth-child(1) {
  border-radius: 8px 0 0 0 !important;
}
.ddpc_sign_table table tr:nth-child(1) th:nth-child(5) {
  border-radius: 0 8px 0 0 !important;
}

.ddpc_sign_table tr:nth-child(n+2) > td {
border-radius: 0 !important;
}

.dd_sign {
  overflow: visible !important;
}
.ddpc_sign_warp {
  overflow: visible !important;
}

.ddpc_sign_list {
  border-bottom: 0px solid #dedede !important;
  line-height: 40px;
  height: 40px;
  font-weight: 700;
}
.ddpc_sign_list ul li.ddpc_on a {
  border-bottom: none !important;
  color: #BA350F;
}

.ddpc_sign_table a {
  color: var(--a-font-color)!important;
  box-shadow: none !important;
}
.ddpc_sign_continuity, .ddpc_sign_rule {
	background: rgba(255, 255, 255, 0.7) !important;
	box-shadow: 0 1px 20px rgba(0, 0, 0, 0.05), inset 0px 0px 60px 20px rgba(255, 255, 255, 0.5) !important;
	border: 1px solid rgba(255, 255, 255, 0.8) !important;
}
.ddpc_sign_btna {
  background: var(--primary-color) !important;
  font-size: 16px !important;
  background-color: #000 !important;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1) !important;
}
.ddpc_sign_btn_grey {
  background: #000 !important;
  color: #fff !important;
  border-radius: 8px !important;
}
.ddpc_sign_info {
	background: rgba(255, 255, 255, 0.7) !important;
	box-shadow: 0 1px 20px rgba(0, 0, 0, 0.05), inset 0px 0px 60px 20px rgba(255, 255, 255, 0.5) !important;
	border: 1px solid rgba(255, 255, 255, 0.8) !important;
}
.ddpc_sign_continuity ul li .ddpc_borderright {
  border-right: 1px solid var(--Second-color) !important;
}
.focus {
  border: none !important;
  border-radius: 8px !important;
  background-color: #ffffffed !important;
  box-shadow: 0 1px 16px rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(50px) !important;
}
.bm {
  border: none;
}
.plugin .bm_h {
  border: none;
  background: none;
}
.wp {
  margin: 0 auto;
  width: 960px !important;
}

/* ——————————————————————————————————高级发帖页—————————————————————————————————————————————————*/
/* 针对可能的内联编辑器 */
[contenteditable="true"] {
  background-color: #fff !important;
  border-radius: 0 !important;
}
.ct2_a.ct2_a_r.wp.cl {
	box-shadow: 0 1px 30px rgba(0, 0, 0, 0.1);
	background: rgba(255, 255, 255, 0.7);
	border: 1px solid rgba(255, 255, 255, 0.5);
   margin: 10px 0 0;
}
#postform #ct {
	padding: 0px !important;
}
#editorbox {
  padding: 10px 0 0 0;
}
#postbox {
  padding: 0px;
}
#postbox input {
  height: 18px;
  margin-right: 4px;
}

#postbox {
  padding: 10px 20px;
}
#editorbox > ul {
  margin: 10px 0 !important;
}
#editorbox > ul > a {
  width: 150px;
}
#postbox > div.pbt.cl {
  margin-bottom: 5px;
}
#typeid_ctrl {
  border: 0px solid var(--Second-color) !important;
  background-color: var(--primary-color) !important;
  color: #fff !important;
  padding: 6px !important;
}
#subject {
  width: 35em;
}
.bbs {
  border-bottom: 0px solid #CDCDCD !important;
}
/* ———提示小按钮—————*/
.ntc_l .d {
  float: right;
  width: 16px;
  height: 16px;
  overflow: hidden;
  text-indent: -9999px;
  border-radius: 50%;
  background-color: #ff1f35a3 !important;
  color: #fff !important;
  position: relative;
  transition: background 0.15s ease;
  background: none;
}
/* 关闭按钮悬停时的效果 */
.ntc_l .d:hover {
  background: red !important;
}
/* 伪元素，用来画出"X" */
.ntc_l .d::before,
.ntc_l .d::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px; /* X线条长度 */
  height: 2px; /* X线条粗细 */
  background: white; /* X线条颜色 */
  transform: translate(-50%, -50%) rotate(45deg); /* 旋转45度 */
  transition: background 0.15s ease; /* 过渡效果 */
}
.ntc_l .d::after {
  transform: translate(-50%, -50%) rotate(-45deg); /* 旋转-45度 */
}

.sltm {
  padding: 5px;
  border: none;
  background-color: #FFF;
  text-align: center;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.15);
}
.sltm li.current,
.sltm li {
  color: var(--primary-font-color);
  text-align: center;
}
.sltm li:hover {
  color: #f26c4f;
  background-color: var(--sider-color);
  text-align: center;
  border-radius: var(--icon-radius);
}
#quota,
#imageCount,
#videoCount,
#resourceSize {
  width: 80px;
}
#e_fontsize:hover,
#e_fontname:hover {
  background-color: #fff !important;
  border: 1px solid var(--Second-color) !important;
  color: var(--primary-color);
  border-radius: var(--icon-radius);
}
/* 更改 输入框 提示词的样式 */
#videoCount::placeholder,
#imageCount::placeholder,
#quota::placeholder,
#resourceSize::placeholder {
color: transparent;    /* 设置为透明 */
}
.ct2_a .tb {
  margin-top: 0;
  padding: 0;
  border: none;
}
.ntc_l {
  background: #ffe9bf;
}
.edt .bar {
  border-bottom: 1px solid var(--Second-color) !important;
  background: rgba(255, 255, 255, 0.5) !important;
  border-radius: 0 !important;
	height: 48px !important;
}
.simpleedt .bar {
	height: 25px  !important;
}

.edt .bar a {
  border: 1px solid transparent !important;
  border-radius: 4px;
  margin: 1px;
}

.edt .bar a:hover, .edt .bar a.hover {
  border-radius: 6px;
  background-color: var(--Second-color) !important;
  border: 1px solid #fff !important;
  margin: 1px;
}

.edt {
  border: 1px solid var(--Second-color) !important;
  overflow: hidden !important;
}
.edt .b2r a.dp {
  margin-right: 3px  !important;
  background: #fff !important;
  border-radius: var(--icon-radius);
  padding-left: 0px !important;
  text-align: center;
  height: 20px !important;
  border: 1px solid var(--Second-color) !important;
  padding-top: 2px;
}
.edt .b2r a {
  height: 22px !important;
}
.edt .btn,
.edt .b1r, .edt .b2r {
  border: none !important;
}
.bar_swch {
  display: block;
  clear: both;
  margin-left: 4px !important;
  padding-top: 5px !important;
}
.edt .bbar {
  border-top: 1px solid var(--Second-color) !important;
  color: var(--primary-font-color) !important;
  background: rgba(255, 255, 255, 0.5) !important;
  border-radius: 0 !important;
}
.edt .bbar a {
  color: var(--primary-font-color) !important;
}
.ntc_l {
  padding: 5px 10px;
  background: #FEFEE9;
  border-radius: 0 !important;
}
.ntc_l.bbs {
  border-bottom: 1px solid var(--Second-color) !important;
  border-radius: 0 !important;
  background: #FFE5B8;
  color: var(--primary-color);
}
#attach_tblheader > tbody > tr > td.atna.pbn {
  padding-bottom: 0px !important;
}
#attach_tblheader {
  border-radius: var(--icon-radius) !important;
  height: 30px;
}
#post_extra_c .exfm {
	margin: 10px;
	background: rgba(255, 255, 255, 0.5) !important;
	border: 1px solid rgba(255, 255, 255, 0.3) !important;
}
#post_extra_tb label span {
  float: left;
  padding: 0 8px 0 8px;
  background: none;
  line-height: 25px;
  border: none !important;
  border-radius: var(--icon-radius) !important;
  color: #fff !important;
  background-color: var(--primary-color) !important;
}
#post_extra_tb label {
  border: none;
  margin-right: 6px;
  height: 25px !important;
}
.pnpost .pn {
  height: 28px;
  font-size: 13px;
  box-shadow:  0px 1px 6px rgba(0, 0, 0, 0.2);
}
.mbm {
  margin-bottom: 5px !important;
}
#e_simple, #e_fullswitcher {
  padding: 2px 8px !important;
  border: 1px solid var(--Second-color) !important;
  background: none !important;
  border-radius: var(--icon-radius) !important;
  background-color: #fff !important;
  height: 18px;
}
#fwin_dialog, #e_image_menu, #e_attach_menu {
	backdrop-filter: blur(30px) saturate(180%);
	border: 1px solid rgba(255, 255, 255);
	box-shadow: 0 1px 100px rgba(0, 0, 0, 0.2), inset 0px 0px 90px 10px rgba(255, 255, 255, 0.93);
	background-color: rgba(244, 246, 248, 0.85);
	border-radius: 10px;
}
#spanButtonPlaceholder,
#imgSpanButtonPlaceholder {
  background-image: none !important;
  background-color: #000;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.20);
  color: #fff;
  width: 90px!important;
  height: 25px!important;
  position: relative;
  padding: 2px 10px;
}
#imgSpanButtonPlaceholder::before {
  content: "选择图片上传";  /* 设置伪元素的内容 */
  position: absolute; /* 定位伪元素 */
  top: 50%; /* 垂直居中 */
  left: 50%; /* 水平居中 */
  transform: translate(-50%, -50%); /* 精确居中 */
  color: #fff; /* 文字颜色 */
  font-size: 13px; /* 字体大小，根据需要调整 */
  white-space: nowrap; /* 强制文字不换行 */
}
#spanButtonPlaceholder::before {
  content: "选择文件上传";  /* 设置伪元素的内容 */
  position: absolute; /* 定位伪元素 */
  top: 50%; /* 垂直居中 */
  left: 50%; /* 水平居中 */
  transform: translate(-50%, -50%); /* 精确居中 */
  color: #fff; /* 文字颜色 */
  font-size: 13px; /* 字体大小，根据需要调整 */
  white-space: nowrap; /* 强制文字不换行 */
}
.imgf .px {
  padding: 4px;
}
.upfl table td {
  border-bottom: 0px dashed var(--Second-color);
  height: 30px;
  line-height: 24px;
  border-radius: 0 !important;
}

.atna p img, .attswf p img {
	border-radius: 0 !important;
}

#editorbox > ul > a:hover {
  background: transparent !important;
  color: var(--primary-color) !important;
}
#fwin_medal {
  position: fixed;
  z-index: 201;
  left: 460px;
  top: 494px;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 10px !important;
  background-color: rgb(255, 255, 255) !important;
}

#delform > table > tbody > tr.th > td.num,
#delform > table > tbody > tr.th > td.by,
#delform > table > tbody > tr.th > td.icn,
#delform > table > tbody > tr.th > td.frm,
#delform > table > tbody > tr.th > th {
  background-color: var(--sider-color) !important;
  border-radius: 0 !important;
  color: var(--a-font-color) !important;
}

#delform > table > tbody > tr.th > td.icn {
  border-radius: 8px 0 0 8px !important;
}
#delform > table > tbody > tr.th > td.by{
  border-radius: 0 8px 8px 0 !important;
}

dl > dd > p:nth-child(4) > strong,
dl > dd > p:nth-child(5) > a{
  color: var(--a-font-color) !important;
}
/* 进度条关闭按钮*/
a.progressCancel {
  display: block;
  float: right;
  width: 16px; /* 修改为16px */
  height: 16px; /* 修改为16px */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
  background: #999; /* 设置背景为黑色 */
  border-radius: 50%; /* 设置圆形按钮 */
  position: relative; /* 确保可以使用伪元素定位 */
  transition: background 0.15s ease; /* 设置背景颜色过渡 */
}
a.progressCancel:hover {
  background: red; /* hover时背景颜色为红色 */
}
a.progressCancel::before,
a.progressCancel::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px; /* 调整线条长度为8px */
  height: 2px; /* "X"的线条粗细 */
  background: white; /* 白色线条 */
  transform: translate(-50%, -50%) rotate(45deg); /* 旋转45度 */
  transition: background 0.15s ease; /* 添加过渡效果 */
}
a.progressCancel::after {
  transform: translate(-50%, -50%) rotate(-45deg); /* 旋转-45度 */
}
/* 进度条关闭按钮结束*/


/* 附件关闭按钮*/
.xld a.d, .xl a.d, .attc a.d, .c a.d, .sinf a.d {
  float: right;
  width: 16px; /* 按钮大小为16px */
  height: 16px; /* 按钮大小为16px */
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
  background: #999; /* 按钮背景颜色为灰色 */
  border-radius: 50%; /* 圆形按钮 */
  position: relative; /* 确保可以使用伪元素定位 */
  transition: background 0.15s ease; /* 背景颜色过渡 */
}
.xld a.d:hover, .xl a.d:hover, .attc a.d:hover, .c a.d:hover, .sinf a.d:hover {
  background: red; /* hover时背景颜色变为较亮的灰色 */
}
.xld a.d::before, .xl a.d::before, .attc a.d::before, .c a.d::before, .sinf a.d::before,
.xld a.d::after, .xl a.d::after, .attc a.d::after, .c a.d::after, .sinf a.d::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px; /* 线条长度为8px */
  height: 2px; /* "X"线条粗细 */
  background: white; /* 交叉线条的颜色为白色 */
  transform: translate(-50%, -50%) rotate(45deg); /* 旋转45度 */
  transition: background 0.15s ease; /* 添加过渡效果 */
}
.xld a.d::after, .xl a.d::after, .attc a.d::after, .c a.d::after, .sinf a.d::after {
  transform: translate(-50%, -50%) rotate(-45deg); /* 旋转-45度 */
}
/* 附件关闭按钮结束*/
#editorbox > ul > li:nth-child(1) > a,
.upfl a, #imgattachlist a {
  color: var(--a-font-color) !important;
}
.imgl img {
  border: 2px solid #ffffff;
  max-width: 110px;
}
.popupfix .px {
  margin-bottom: 4px;
  border-radius: var(--icon-radius);
}
.imgl {
  background-color: #66666614;
}
.pnc, a.pnc {
  font-size: 13px;
}
.enter-btn {
background: #FFF;
color: #000;
}
button.pn.vm {
  background-color: var(--primary-color) !important;
}
#spanButtonPlaceholder *,
#imgSpanButtonPlaceholder *{
  position: absolute;
  inset: 0;
  width: 110px;
  height: 30px;
  overflow: hidden;
}
.p_opt .txt, .p_opt .txtarea {
	border: 1px solid var(--primary-color);
}
.edt .area {
    border-radius: 0;
}
#e_iframe {
	border-radius: 0;
}
#post_extra_tb span.a {
    background-image: none !important;
}

.edt .pt {
	padding: 0 !important;
	width: 100% !important;
	height: 600px;
	border: none;
	font-size: 14px;
}
.fa_rss {
    height: 12px;
    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGNsYXNzPSJpb25pY29uIiB2aWV3Qm94PSIwIDAgNTEyIDUxMiI+PHBhdGggZD0iTTEwOC41NiAzNDIuNzhhNjAuMzQgNjAuMzQgMCAxMDYwLjU2IDYwLjQ0IDYwLjYzIDYwLjYzIDAgMDAtNjAuNTYtNjAuNDR6Ii8+PHBhdGggZD0iTTQ4IDE4Ni42N3Y4Ni41NWM1MiAwIDEwMS45NCAxNS4zOSAxMzguNjcgNTIuMTFzNTIgODYuNTYgNTIgMTM4LjY3aDg2LjY2YzAtMTUxLjU2LTEyNS42Ni0yNzcuMzMtMjc3LjMzLTI3Ny4zM3oiLz48cGF0aCBkPSJNNDggNDh2ODYuNTZjMTg1LjI1IDAgMzI5LjIyIDE0NC4wOCAzMjkuMjIgMzI5LjQ0SDQ2NEM0NjQgMjM0LjY2IDI3Ny42NyA0OCA0OCA0OHoiLz48L3N2Zz4=);
    display: block;
    line-height: 12px;
    margin: 12px 0px 0px 0px;
}
.fpd a.fbld {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0xMS42MyA3LjgyQzEyLjQ2IDcuMjQgMTMgNi4zOCAxMyA1LjUgMTMgMy41NyAxMS40MyAyIDkuNSAySDR2MTJoNi4yNWMxLjc5IDAgMy4yNS0xLjQ2IDMuMjUtMy4yNSAwLTEuMy0uNzctMi40MS0xLjg3LTIuOTN6TTYuNSA0aDIuNzVjLjgzIDAgMS41LjY3IDEuNSAxLjVTMTAuMDggNyA5LjI1IDdINi41VjR6bTMuMjUgOEg2LjVWOWgzLjI1Yy44MyAwIDEuNS42NyAxLjUgMS41cy0uNjcgMS41LTEuNSAxLjV6Ii8+CiAgICA8cGF0aCBkPSJNMCAwaDE4djE4SDB6IiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo=) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;

    }
#e_bold {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0xMS42MyA3LjgyQzEyLjQ2IDcuMjQgMTMgNi4zOCAxMyA1LjUgMTMgMy41NyAxMS40MyAyIDkuNSAySDR2MTJoNi4yNWMxLjc5IDAgMy4yNS0xLjQ2IDMuMjUtMy4yNSAwLTEuMy0uNzctMi40MS0xLjg3LTIuOTN6TTYuNSA0aDIuNzVjLjgzIDAgMS41LjY3IDEuNSAxLjVTMTAuMDggNyA5LjI1IDdINi41VjR6bTMuMjUgOEg2LjVWOWgzLjI1Yy44MyAwIDEuNS42NyAxLjUgMS41cy0uNjcgMS41LTEuNSAxLjV6Ii8+CiAgICA8cGF0aCBkPSJNMCAwaDE4djE4SDB6IiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo=) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
    }
#e_italic {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik03IDJ2MmgyLjU4bC0zLjY2IDhIM3YyaDh2LTJIOC40MmwzLjY2LThIMTVWMnoiLz4KICAgIDxwYXRoIGQ9Ik0wIDBoMTh2MThIMHoiIGZpbGw9Im5vbmUiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
    }
#e_underline {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik05IDEzYzIuNzYgMCA1LTIuMjQgNS01VjFoLTIuNXY3YzAgMS4zOC0xLjEyIDIuNS0yLjUgMi41UzYuNSA5LjM4IDYuNSA4VjFINHY3YzAgMi43NiAyLjI0IDUgNSA1em0tNiAydjJoMTJ2LTJIM3oiLz4KICAgIDxwYXRoIGQ9Ik0wIDBoMTh2MThIMHoiIGZpbGw9Im5vbmUiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
    }
.fpd a.fclr {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGZpbGwtb3BhY2l0eT0iLjM2IiBkPSJNMCAxNWgxOHYzSDB6Ii8+CiAgICA8cGF0aCBkPSJNMCAwaDE4djE4SDB6IiBmaWxsPSJub25lIi8+CiAgICA8cGF0aCBkPSJNMTAgMUg4TDMuNSAxM2gybDEuMTItM2g0Ljc1bDEuMTIgM2gyTDEwIDF6TTcuMzggOEw5IDMuNjcgMTAuNjIgOEg3LjM4eiIvPgo8L3N2Zz4K) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
    }
.fpd a.flnk {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0wIDBoMTh2MThIMHoiIGZpbGw9Im5vbmUiLz4KICAgIDxwYXRoIGQ9Ik0xLjkgOWMwLTEuMTYuOTQtMi4xIDIuMS0yLjFoNFY1SDRDMS43OSA1IDAgNi43OSAwIDlzMS43OSA0IDQgNGg0di0xLjlINGMtMS4xNiAwLTIuMS0uOTQtMi4xLTIuMXpNMTQgNWgtNHYxLjloNGMxLjE2IDAgMi4xLjk0IDIuMSAyLjEgMCAxLjE2LS45NCAyLjEtMi4xIDIuMWgtNFYxM2g0YzIuMjEgMCA0LTEuNzkgNC00cy0xLjc5LTQtNC00em0tOCA1aDZWOEg2djJ6Ii8+Cjwvc3ZnPgo=) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;

    }
.fpd a.fmg {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij4KICAgIDxwYXRoIGQ9Ik0wIDBoMjR2MjRIMHoiIGZpbGw9Im5vbmUiLz4KICAgIDxwYXRoIGQ9Ik0yMSAxOVY1YzAtMS4xLS45LTItMi0ySDVjLTEuMSAwLTIgLjktMiAydjE0YzAgMS4xLjkgMiAyIDJoMTRjMS4xIDAgMi0uOSAyLTJ6TTguNSAxMy41bDIuNSAzLjAxTDE0LjUgMTJsNC41IDZINWwzLjUtNC41eiIvPgo8L3N2Zz4K) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;

}
.fpd a.fsml {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0wIDBoMTh2MThIMHoiIGZpbGw9Im5vbmUiLz4KICAgIDxwYXRoIGQ9Ik02IDhjLjU1IDAgMS0uNDUgMS0xcy0uNDUtMS0xLTEtMSAuNDUtMSAxIC40NSAxIDEgMXptNiAwYy41NSAwIDEtLjQ1IDEtMXMtLjQ1LTEtMS0xLTEgLjQ1LTEgMSAuNDUgMSAxIDF6bS0zIDUuNWMyLjE0IDAgMy45Mi0xLjUgNC4zOC0zLjVINC42MmMuNDYgMiAyLjI0IDMuNSA0LjM4IDMuNXpNOSAxQzQuNTcgMSAxIDQuNTggMSA5czMuNTcgOCA4IDggOC0zLjU4IDgtOC0zLjU4LTgtOC04em0wIDE0LjVjLTMuNTkgMC02LjUtMi45MS02LjUtNi41UzUuNDEgMi41IDkgMi41czYuNSAyLjkxIDYuNSA2LjUtMi45MSA2LjUtNi41IDYuNXoiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;

}
.fpd a.fqt {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0xMCA1djVoMi43NUwxMSAxM2gyLjI1TDE1IDEwVjVoLTV6bS03IDVoMi43NUw0IDEzaDIuMjVMOCAxMFY1SDN2NXoiLz4KICAgIDxwYXRoIGQ9Ik0wIDBoMTh2MThIMHoiIGZpbGw9Im5vbmUiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;

}
.fpd a.fcd {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij4KICAgIDxwYXRoIGZpbGw9Im5vbmUiIGQ9Ik0wIDBoMjR2MjRIMFYweiIvPgogICAgPHBhdGggZD0iTTkuNCAxNi42TDQuOCAxMmw0LjYtNC42TDggNmwtNiA2IDYgNiAxLjQtMS40em01LjIgMGw0LjYtNC42LTQuNi00LjZMMTYgNmw2IDYtNiA2LTEuNC0xLjR6Ii8+Cjwvc3ZnPgo=) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;

}
#e_quote {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0xMCA1djVoMi43NUwxMSAxM2gyLjI1TDE1IDEwVjVoLTV6bS03IDVoMi43NUw0IDEzaDIuMjVMOCAxMFY1SDN2NXoiLz4KICAgIDxwYXRoIGQ9Ik0wIDBoMTh2MThIMHoiIGZpbGw9Im5vbmUiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_code {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij4KICAgIDxwYXRoIGZpbGw9Im5vbmUiIGQ9Ik0wIDBoMjR2MjRIMFYweiIvPgogICAgPHBhdGggZD0iTTkuNCAxNi42TDQuOCAxMmw0LjYtNC42TDggNmwtNiA2IDYgNiAxLjQtMS40em01LjIgMGw0LjYtNC42LTQuNi00LjZMMTYgNmw2IDYtNiA2LTEuNC0xLjR6Ii8+Cjwvc3ZnPgo=) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_forecolor {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGZpbGwtb3BhY2l0eT0iLjM2IiBkPSJNMCAxNWgxOHYzSDB6Ii8+CiAgICA8cGF0aCBkPSJNMCAwaDE4djE4SDB6IiBmaWxsPSJub25lIi8+CiAgICA8cGF0aCBkPSJNMTAgMUg4TDMuNSAxM2gybDEuMTItM2g0Ljc1bDEuMTIgM2gyTDEwIDF6TTcuMzggOEw5IDMuNjcgMTAuNjIgOEg3LjM4eiIvPgo8L3N2Zz4K) no-repeat center center;
    background-size: 14px !important;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
    }
#e_backcolor {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGZpbGwtb3BhY2l0eT0iLjM2IiBkPSJNMCAxNWgxOHYzSDB6Ii8+CiAgICA8cGF0aCBkPSJNMTQuNSA4Ljg3UzEzIDEwLjQ5IDEzIDExLjQ5YzAgLjgzLjY3IDEuNSAxLjUgMS41czEuNS0uNjcgMS41LTEuNWMwLS45OS0xLjUtMi42Mi0xLjUtMi42MnptLTEuNzktMi4wOEw1LjkxIDAgNC44NSAxLjA2bDEuNTkgMS41OS00LjE1IDQuMTRjLS4zOS4zOS0uMzkgMS4wMiAwIDEuNDFsNC41IDQuNWMuMi4yLjQ1LjMuNzEuM3MuNTEtLjEuNzEtLjI5bDQuNS00LjVjLjM5LS4zOS4zOS0xLjAzIDAtMS40MnpNNC4yMSA3TDcuNSAzLjcxIDEwLjc5IDdINC4yMXoiLz4KICAgIDxwYXRoIGQ9Ik0wIDBoMTh2MThIMHoiIGZpbGw9Im5vbmUiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
    }

#e_autotypeset {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0yIDE2aDE0di0ySDJ2MnptMC00aDE0di0ySDJ2MnpNMiAydjJoMTRWMkgyem0wIDZoMTRWNkgydjJ6Ii8+CiAgICA8cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDE4djE4SDB6Ii8+Cjwvc3ZnPgo=) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_justifyleft {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0yIDE2aDEwdi0ySDJ2MnpNMTIgNkgydjJoMTBWNnpNMiAydjJoMTRWMkgyem0wIDEwaDE0di0ySDJ2MnoiLz4KICAgIDxwYXRoIGZpbGw9Im5vbmUiIGQ9Ik0wIDBoMTh2MThIMHoiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_tbl {
    background: transparent url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIA0KICB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciDQogIHdpZHRoPSIyNCINCiAgaGVpZ2h0PSIyNCINCiAgdmlld0JveD0iMCAwIDI0IDI0Ig0KICBmaWxsPSJub25lIg0KICBzdHJva2U9IiMwMDAwMDAiDQogIHN0cm9rZS13aWR0aD0iMiINCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIg0KICBzdHJva2UtbGluZWpvaW49InJvdW5kIg0KPg0KICA8cmVjdCB4PSIzIiB5PSIzIiB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHJ4PSIyIiByeT0iMiIgLz4NCiAgPGxpbmUgeDE9IjMiIHkxPSI5IiB4Mj0iMjEiIHkyPSI5IiAvPg0KICA8bGluZSB4MT0iMyIgeTE9IjE1IiB4Mj0iMjEiIHkyPSIxNSIgLz4NCiAgPGxpbmUgeDE9IjkiIHkxPSI5IiB4Mj0iOSIgeTI9IjIxIiAvPg0KICA8bGluZSB4MT0iMTUiIHkxPSI5IiB4Mj0iMTUiIHkyPSIyMSIgLz4NCjwvc3ZnPg==) no-repeat center center;
    background-size: 16px;
    width: 20px !important;
    height: 22px !important;
    background-position: 3px 3px !important;
}
#e_justifycenter {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik00IDE0djJoMTB2LTJINHptMC04djJoMTBWNkg0em0tMiA2aDE0di0ySDJ2MnpNMiAydjJoMTRWMkgyeiIvPgo8L3N2Zz4K) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 3px 4px !important;
}
#e_justifyright {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik02IDE2aDEwdi0ySDZ2MnptLTQtNGgxNHYtMkgydjJ6TTIgMnYyaDE0VjJIMnptNCA2aDEwVjZINnYyeiIvPgogICAgPHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxOHYxOEgweiIvPgo8L3N2Zz4K) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_insertorderedlist {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0yIDEzaDJ2LjVIM3YxaDF2LjVIMnYxaDN2LTRIMnYxem0wLTVoMS44TDIgMTAuMXYuOWgzdi0xSDMuMkw1IDcuOVY3SDJ2MXptMS0yaDFWMkgydjFoMXYzem00LTN2Mmg5VjNIN3ptMCAxMmg5di0ySDd2MnptMC01aDlWOEg3djJ6Ii8+Cjwvc3ZnPgo=) no-repeat center center;
    background-size: 16px;
    width: 20px !important;
    height: 22px !important;
    background-position: 3px 3px !important;
}
#e_insertunorderedlist {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik03IDEwaDlWOEg3djJ6bTAtN3YyaDlWM0g3em0wIDEyaDl2LTJIN3Yyem0tNC01aDJWOEgzdjJ6bTAtN3YyaDJWM0gzem0wIDEyaDJ2LTJIM3YyeiIvPgo8L3N2Zz4K) no-repeat center center;
    background-size: 16px;
    width: 20px !important;
    height: 22px !important;
    background-position: 2px 3px !important;
}
#e_removeformat {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0wIDBoMTh2MThIMHptMCAwaDE4djE4SDB6bTAgMGgxOHYxOEgweiIgZmlsbD0ibm9uZSIvPgogICAgPHBhdGggZD0iTTIuMjcgNC41NUw3LjQzIDkuNyA1IDE1aDIuNWwxLjY0LTMuNThMMTMuNzMgMTYgMTUgMTQuNzMgMy41NSAzLjI3IDIuMjcgNC41NXpNNS44MiAzbDIgMmgxLjc2bC0uNTUgMS4yMSAxLjcxIDEuNzFMMTIuMDggNUgxNlYzSDUuODJ6Ii8+Cjwvc3ZnPgo=) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_inserthorizontalrule {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCI+CiAgICA8ZGVmcz4KICAgICAgICA8cGF0aCBpZD0iYSIgZD0iTTAgMGgyNHYyNEgwVjB6Ii8+CiAgICA8L2RlZnM+CiAgICA8Y2xpcFBhdGggaWQ9ImIiPgogICAgICAgIDx1c2UgeGxpbms6aHJlZj0iI2EiIG92ZXJmbG93PSJ2aXNpYmxlIi8+CiAgICA8L2NsaXBQYXRoPgogICAgPHBhdGggY2xpcC1wYXRoPSJ1cmwoI2IpIiBkPSJNMjAgOUg0djJoMTZWOXpNNCAxNWgxNnYtMkg0djJ6Ii8+Cjwvc3ZnPgo=) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_sml {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0wIDBoMTh2MThIMHoiIGZpbGw9Im5vbmUiLz4KICAgIDxwYXRoIGQ9Ik02IDhjLjU1IDAgMS0uNDUgMS0xcy0uNDUtMS0xLTEtMSAuNDUtMSAxIC40NSAxIDEgMXptNiAwYy41NSAwIDEtLjQ1IDEtMXMtLjQ1LTEtMS0xLTEgLjQ1LTEgMSAuNDUgMSAxIDF6bS0zIDUuNWMyLjE0IDAgMy45Mi0xLjUgNC4zOC0zLjVINC42MmMuNDYgMiAyLjI0IDMuNSA0LjM4IDMuNXpNOSAxQzQuNTcgMSAxIDQuNTggMSA5czMuNTcgOCA4IDggOC0zLjU4IDgtOC0zLjU4LTgtOC04em0wIDE0LjVjLTMuNTkgMC02LjUtMi45MS02LjUtNi41UzUuNDEgMi41IDkgMi41czYuNSAyLjkxIDYuNSA2LjUtMi45MSA2LjUtNi41IDYuNXoiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 25px;
    width: 35px !important;
    height: 15px !important;
    background-position: 6px 4px !important;
}
#e_image {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij4KICAgIDxwYXRoIGQ9Ik0wIDBoMjR2MjRIMHoiIGZpbGw9Im5vbmUiLz4KICAgIDxwYXRoIGQ9Ik0yMSAxOVY1YzAtMS4xLS45LTItMi0ySDVjLTEuMSAwLTIgLjktMiAydjE0YzAgMS4xLjkgMiAyIDJoMTRjMS4xIDAgMi0uOSAyLTJ6TTguNSAxMy41bDIuNSAzLjAxTDE0LjUgMTJsNC41IDZINWwzLjUtNC41eiIvPgo8L3N2Zz4K) no-repeat center center;
    background-size: 25px;
    width: 35px !important;
    height: 15px !important;
    background-position: 6px 4px !important;
}
#e_attach {
    background: transparent url(data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) no-repeat center center;
    background-size: 25px !important;
    width: 35px !important;
    height: 15px !important;
    background-position: 6px 4px !important;
}
#e_free {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij4KICAgIDxwYXRoIGZpbGw9Im5vbmUiIGQ9Ik0wIDBoMjR2MjRIMFYweiIvPgogICAgPHBhdGggZD0iTTIwIDNINHYxMGMwIDIuMjEgMS43OSA0IDQgNGg2YzIuMjEgMCA0LTEuNzkgNC00di0zaDJjMS4xMSAwIDItLjkgMi0yVjVjMC0xLjExLS44OS0yLTItMnptMCA1aC0yVjVoMnYzek00IDE5aDE2djJINHoiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_index {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik04IDEyaDh2LTJIOHYyem0wLTRoOFY2SDh2MnptOCA2SDJ2MmgxNHYtMnpNMiA5bDMuNSAzLjV2LTdMMiA5em0wLTd2MmgxNFYySDJ6Ii8+CiAgICA8cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDE4djE4SDBWMHoiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_page {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik01LjUgMTBIMnY1YzAgLjU1LjQ1IDEgMSAxaDV2LTMuNWwtMy41IDEgMS0zLjV6TTIgM3Y1aDMuNWwtMS0zLjUgMy41IDFWMkgzYy0uNTUgMC0xIC40NS0xIDF6bTEzLTFoLTV2My41bDMuNS0xLTEgMy41SDE2VjNjMC0uNTUtLjQ1LTEtMS0xem0tMS41IDExLjVsLTMuNS0xVjE2aDVjLjU1IDAgMS0uNDUgMS0xdi01aC0zLjVsMSAzLjV6Ii8+CiAgICA8cGF0aCBkPSJNMCAwaDE4djE4SDB6IiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo=) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_undo {
    background: transparent url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyBmaWxsPSIjMDAwMDAwIiB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgNTEyIDUxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8dGl0bGU+dW5kbzwvdGl0bGU+CiAgPHBhdGggZD0iTTQ0OCAzODRRMzg5IDMzNiAzMzUgMzEyIDI4MCAyODggMjI0IDI4OEwyMjQgMzgwIDYwIDIxNiAyMjQgNTIgMjI0IDE0NFEzMjAgMTY2IDM3NCAyMjIgNDI4IDI3NyA0NDggMzg0WiIvPgo8L3N2Zz4=) no-repeat center center;
    background-size: 14px !important;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_redo {
    background: transparent url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyBmaWxsPSIjMDAwMDAwIiB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgNTEyIDUxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8dGl0bGU+cmVkbzwvdGl0bGU+CiAgPHBhdGggZD0iTTY0IDM4NFE4NCAyNzcgMTM4IDIyMiAxOTIgMTY2IDI4OCAxNDRMMjg4IDUyIDQ1MiAyMTYgMjg4IDM4MCAyODggMjg4UTIzMiAyODggMTc4IDMxMiAxMjMgMzM2IDY0IDM4NFoiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 14px !important;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_password {
	background: transparent url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgNDggNDgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGcgaWQ9IkxheWVyXzIiIGRhdGEtbmFtZT0iTGF5ZXIgMiI+CiAgICA8ZyBpZD0iaW52aXNpYmxlX2JveCIgZGF0YS1uYW1lPSJpbnZpc2libGUgYm94Ij4KICAgICAgPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSJub25lIi8+CiAgICA8L2c+CiAgICA8ZyBpZD0iTGF5ZXJfNyIgZGF0YS1uYW1lPSJMYXllciA3Ij4KICAgICAgPHBhdGggZD0iTTM5LDE4SDM1VjEzQTExLDExLDAsMCwwLDI0LDJIMjJBMTEsMTEsMCwwLDAsMTEsMTN2NUg3YTIsMiwwLDAsMC0yLDJWNDRhMiwyLDAsMCwwLDIsMkgzOWEyLDIsMCwwLDAsMi0yVjIwQTIsMiwwLDAsMCwzOSwxOFpNMTUsMTNhNyw3LDAsMCwxLDctN2gyYTcsNywwLDAsMSw3LDd2NUgxNVpNMTQsMzVhMywzLDAsMSwxLDMtM0EyLjksMi45LDAsMCwxLDE0LDM1Wm05LDBhMywzLDAsMSwxLDMtM0EyLjksMi45LDAsMCwxLDIzLDM1Wm05LDBhMywzLDAsMSwxLDMtM0EyLjksMi45LDAsMCwxLDMyLDM1WiIvPgogICAgPC9nPgogIDwvZz4KPC9zdmc+) no-repeat center center;
	background-size: 14px !important;
	width: 20px !important;
	height: 22px !important;
	background-position: 4px 4px !important;
}
#e_postbg {
    background: transparent url(data:image/svg+xml;base64,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) no-repeat center center;
    background-size: 13px !important;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_unlink {
    background: transparent url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyBmaWxsPSIjMDAwMDAwIiB2ZXJzaW9uPSIxLjEiIGlkPSJMYXllcl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNHB4IiBoZWlnaHQ9IjI0cHgiIHZpZXdCb3g9IjAgMCAxMDAgMTAwIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCAxMDAgMTAwIj4KICA8Zz4KICAgIDxwYXRoIGQ9Ik0xNi4yMDEsMjAuMzg2bDkuNzE5LDkuNzIxYzEuMTIsMS4xMTgsMi45MzUsMS4xMTgsNC4wNTQsMGMxLjExOC0xLjEyMSwxLjExOC0yLjkzMywwLTQuMDU1bC05LjY3OS05LjY3NyBjLTAuMDAyLTAuMDAzLTAuMDA0LTAuMDA1LTAuMDA2LTAuMDA4cy0wLjAwNS0wLjAwNC0wLjAwOC0wLjAwNmwtMC4wMjYtMC4wMjZsLTAuMDAzLDAuMDAzYy0xLjEyMi0xLjA4NS0yLjkwOS0xLjA3Ny00LjAxNywwLjAzIGMtMS4xMDcsMS4xMDgtMS4xMTUsMi44OTUtMC4wMyw0LjAxNUwxNi4yMDEsMjAuMzg2eiIvPgogICAgPHBhdGggZD0iTTMyLjg5OCwxNi41NDZsMy41NTgsMTMuMjc3YzAuNDEsMS41MjksMS45ODEsMi40MzcsMy41MSwyLjAyNmMxLjUyNy0wLjQwOSwyLjQzNS0xLjk4MSwyLjAyNy0zLjUxbC0zLjU0NS0xMy4yMjQgYzAtMC4wMDIsMC0wLjAwNS0wLjAwMS0wLjAwN3MtMC4wMDItMC4wMDQtMC4wMDMtMC4wMDdsLTAuMDExLTAuMDRsLTAuMDA0LDAuMDAxYy0wLjQyOS0xLjUtMS45OC0yLjM4Ny0zLjQ5My0xLjk4MiBjLTEuNTEzLDAuNDA1LTIuNDEzLDEuOTQ5LTIuMDM1LDMuNDYzTDMyLjg5OCwxNi41NDZ6Ii8+CiAgICA8cGF0aCBkPSJNMTYuMDU0LDM5LjQyM2wtMC4wMDEsMC4wMDVsMC4wNDYsMC4wMTJjMCwwLDAuMDAxLDAsMC4wMDEsMGwwLjAwMSwwbDEzLjIyOSwzLjU0NGMxLjUzLDAuNDEsMy4xMDEtMC40OTgsMy41MTEtMi4wMjUgYzAuNDEtMS41MjktMC40OTgtMy4xMDEtMi4wMjYtMy41MUwxNy42LDMzLjkwOGMtMC4wMDUtMC4wMDItMC4wMS0wLjAwNS0wLjAxNi0wLjAwNnMtMC4wMTEtMC4wMDEtMC4wMTctMC4wMDNsLTAuMDMtMC4wMDggbC0wLjAwMSwwLjAwM2MtMS41MTUtMC4zNzctMy4wNTgsMC41MjMtMy40NjMsMi4wMzVDMTMuNjY4LDM3LjQ0MiwxNC41NTUsMzguOTkyLDE2LjA1NCwzOS40MjN6Ii8+CiAgICA8cGF0aCBkPSJNODMuNzk4LDc5LjYxM2wtOS43Mi05LjcyYy0xLjExOS0xLjExOS0yLjkzNC0xLjExOS00LjA1MywwYy0xLjExOCwxLjEyLTEuMTE4LDIuOTMzLDAsNC4wNTVsOS42NzksOS42NzcgYzAuMDAyLDAuMDAzLDAuMDA0LDAuMDA1LDAuMDA2LDAuMDA4YzAuMDAzLDAuMDAyLDAuMDA1LDAuMDA0LDAuMDA4LDAuMDA2bDAuMDI2LDAuMDI2bDAuMDAzLTAuMDAzIGMxLjEyMiwxLjA4NSwyLjkwOCwxLjA3Nyw0LjAxNy0wLjAzYzEuMTA3LTEuMTA4LDEuMTE1LTIuODk1LDAuMDMtNC4wMTZMODMuNzk4LDc5LjYxM3oiLz4KICAgIDxwYXRoIGQ9Ik02Ny4xMDEsODMuNDU0bC0zLjU1OS0xMy4yNzdjLTAuNDEtMS41MjktMS45OC0yLjQzNi0zLjUxLTIuMDI2Yy0xLjUzLDAuNDA5LTIuNDM2LDEuOTgxLTIuMDI3LDMuNTA5bDMuNTQ2LDEzLjIzMSBjMCwwLDAsMC4wMDEsMCwwLjAwMWMwLDAuMDAxLDAsMC4wMDEsMCwwLjAwMWwwLjAxMiwwLjA0NWwwLjAwNS0wLjAwMWMwLjQyOCwxLjUwMSwxLjk4LDIuMzg3LDMuNDkzLDEuOTgzIGMxLjUxMy0wLjQwNiwyLjQxMi0xLjk1LDIuMDM0LTMuNDYzTDY3LjEwMSw4My40NTR6Ii8+CiAgICA8cGF0aCBkPSJNODMuOTQzLDYwLjU3N2wwLjAwMS0wLjAwNGwtMC4wNDEtMC4wMTFjLTAuMDAyLDAtMC4wMDMtMC4wMDEtMC4wMDUtMC4wMDJjLTAuMDAyLDAtMC4wMDMsMC0wLjAwNS0wLjAwMWwtMTMuMjI2LTMuNTQ0IGMtMS41My0wLjQxLTMuMSwwLjQ5OS0zLjUxMSwyLjAyNmMtMC40MDksMS41MjksMC40OTgsMy4xLDIuMDI3LDMuNTExbDEzLjIyNCwzLjU0MmMwLjAwMiwwLjAwMSwwLjAwNCwwLjAwMiwwLjAwNiwwLjAwMiBjMC4wMDEsMC4wMDEsMC4wMDUsMCwwLjAwNywwLjAwMWwwLjA0LDAuMDExbDAuMDAxLTAuMDA0YzEuNTE0LDAuMzc4LDMuMDU4LTAuNTIyLDMuNDYyLTIuMDM1IEM4Ni4zMzEsNjIuNTU3LDg1LjQ0Myw2MS4wMDYsODMuOTQzLDYwLjU3N3oiLz4KICAgIDxwYXRoIGQ9Ik00OC4yMTIsNTEuNzU2Yy03LjU1Mi03LjU1Mi0xOS42NDgtNy43OS0yNy40ODYtMC43MTNsLTAuMDE5LTAuMDE5TDEwLjYxLDYxLjEyMWMtNy43OTcsNy43OTctNy43OTcsMjAuNDQsMCwyOC4yMzcgYzcuNzk3LDcuNzk4LDIwLjQzOSw3Ljc5OCwyOC4yMzcsMGwxMC4wOTgtMTAuMDk4bC0wLjAxOS0wLjAxOUM1Ni4wMDEsNzEuNDA0LDU1Ljc2NCw1OS4zMDgsNDguMjEyLDUxLjc1NnogTTQxLjY1OSw3Mi41NTggbC0wLjYxOSwwLjYxOWwtMC4wMDEsMC4wMDFsLTAuMDAxLDBsLTkuMDA1LDkuMDA1bC0wLjAwMSwwLjAwMWMtMy45MzUsMy45MzUtMTAuMzE0LDMuOTM1LTE0LjI0OCwwcy0zLjkzNS0xMC4zMTQsMC0xNC4yNDggbDAuMDAxLTAuMDAxbDkuMDA1LTkuMDA2bDAuMDAxLTAuMDAxbDAuMDAxLTAuMDAxbDAuNjE5LTAuNjE5bDAuMDI5LDAuMDI4YzMuOTU5LTMuMzI5LDkuODc0LTMuMTM0LDEzLjYsMC41OTEgYzMuNzI2LDMuNzI2LDMuOTIxLDkuNjQyLDAuNTkxLDEzLjZMNDEuNjU5LDcyLjU1OHoiLz4KICAgIDxwYXRoIGQ9Ik04OS4zODksMTAuNjQxYy03LjU1Mi03LjU1Mi0xOS42NDgtNy43OS0yNy40ODYtMC43MTNsLTAuMDE5LTAuMDE5TDUxLjc4NywyMC4wMDZjLTcuNzk3LDcuNzk3LTcuNzk3LDIwLjQ0LDAsMjguMjM3IGM3Ljc5Nyw3Ljc5OCwyMC40MzksNy43OTgsMjguMjM3LDBsMTAuMDk4LTEwLjA5OGwtMC4wMTktMC4wMTlDOTcuMTc4LDMwLjI4OSw5Ni45NDEsMTguMTkzLDg5LjM4OSwxMC42NDF6IE04Mi44MzYsMzEuNDQzIGwtMC42MTksMC42MTlsLTAuMDAxLDAuMDAxbC0wLjAwMSwwbC05LjAwNSw5LjAwNWwtMC4wMDEsMC4wMDFjLTMuOTM1LDMuOTM1LTEwLjMxNCwzLjkzNS0xNC4yNDgsMCBjLTMuOTM1LTMuOTM1LTMuOTM1LTEwLjMxNCwwLTE0LjI0OGwwLjAwMS0wLjAwMWw5LjAwNS05LjAwNmMwLDAsMCwwLDAuMDAxLTAuMDAxbDAuMDAxLTAuMDAxbDAuNjE5LTAuNjE5bDAuMDI5LDAuMDI4IGMzLjk1OS0zLjMyOSw5Ljg3NC0zLjEzNCwxMy42LDAuNTkxczMuOTIxLDkuNjQyLDAuNTkxLDEzLjZMODIuODM2LDMxLjQ0M3oiLz4KICA8L2c+Cjwvc3ZnPg==) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
#e_floatleft {
    background: transparent url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgMTggMTgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHBhdGggZD0iTTEuNSAwQzAuNjcxNTczIDAgMCAwLjY3MTU3MyAwIDEuNVY1LjVDMCA2LjMyODQzIDAuNjcxNTczIDcgMS41IDdINS41QzYuMzI4NDMgNyA3IDYuMzI4NDMgNyA1LjVWMS41QzcgMC42NzE1NzMgNi4zMjg0MyAwIDUuNSAwSDEuNVoiIGZpbGw9IiMwMDAwMDAiLz4KICA8cGF0aCBkPSJNOSAySDE1VjFIOVYyWiIgZmlsbD0iIzAwMDAwMCIvPgogIDxwYXRoIGQ9Ik05IDZIMTVWNUg5VjZaIiBmaWxsPSIjMDAwMDAwIi8+CiAgPHBhdGggZD0iTTAgMTBIMTVWOUgwVjEwWiIgZmlsbD0iIzAwMDAwMCIvPgogIDxwYXRoIGQ9Ik0wIDE0SDE1VjEzSDBWMTRaIiBmaWxsPSIjMDAwMDAwIi8+Cjwvc3ZnPg==) no-repeat center center;
    background-size: 14px !important;
    width: 20px !important;
    height: 22px !important;
    background-position: 5px 5px !important;
}
#e_floatright {
    background: transparent url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgMTUgMTUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHBhdGggZD0iTTkuNSAwQzguNjcxNTcgMCA4IDAuNjcxNTczIDggMS41VjUuNUM4IDYuMzI4NDMgOC42NzE1NyA3IDkuNSA3SDEzLjVDMTQuMzI4NCA3IDE1IDYuMzI4NDMgMTUgNS41VjEuNUMxNSAwLjY3MTU3MyAxNC4zMjg0IDAgMTMuNSAwSDkuNVoiIGZpbGw9IiMwMDAwMDAiLz4KICA8cGF0aCBkPSJNMCAySDZWMUgwVjJaIiBmaWxsPSIjMDAwMDAwIi8+CiAgPHBhdGggZD0iTTAgNkg2VjVIMFY2WiIgZmlsbD0iIzAwMDAwMCIvPgogIDxwYXRoIGQ9Ik0wIDEwSDE1VjlIMFYxMFoiIGZpbGw9IiMwMDAwMDAiLz4KICA8cGF0aCBkPSJNMCAxNEgxNUYxM0gwVjE0WiIgZmlsbD0iIzAwMDAwMCIvPgo8L3N2Zz4=) no-repeat center center;
    background-size: 12px !important;
    width: 20px !important;
    height: 22px !important;
    background-position: 5px 5px !important;
}
#e_pasteword {
    background: transparent url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgMTkyIDE5MiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBmaWxsPSJub25lIj4KICA8cGF0aCBkPSJNNTYgMzBjMC0xLjY2MiAxLjMzOC0zIDMtM2gxMDhjMS42NjIgMCAzIDEuMzM4IDMgM3YxMzJjMCAxLjY2Mi0xLjMzOCAzLTMgM0g1OWMtMS42NjIgMC0zLTEuMzM4LTMtM3YtMzJtMC02OFYzMCIgc3R5bGU9ImZpbGwtb3BhY2l0eTouNDAyNjU4O3N0cm9rZTojMDAwMDAwO3N0cm9rZS13aWR0aDoxMjtzdHJva2UtbGluZWNhcDpyb3VuZDtwYWludC1vcmRlcjpzdHJva2UgZmlsbCBtYXJrZXJzIi8+CiAgPHJlY3Qgd2lkdGg9IjY4IiBoZWlnaHQ9IjY4IiB4PSItNTguMSIgeT0iNDAuMyIgcng9IjMiIHN0eWxlPSJmaWxsOm5vbmU7ZmlsbC1vcGFjaXR5Oi40MDI2NTg7c3Ryb2tlOiMwMDAwMDA7c3Ryb2tlLXdpZHRoOjEyO3N0cm9rZS1saW5lY2FwOnJvdW5kO3N0cm9rZS1saW5lam9pbjptaXRlcjtzdHJva2UtZGFzaGFycmF5Om5vbmU7c3Ryb2tlLW9wYWNpdHk6MTtwYWludC1vcmRlcjpzdHJva2UgZmlsbCBtYXJrZXJzIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg4MC4xIDIxLjcpIi8+CiAgPHBhdGggZD0iTTU1Ljk0NCA1OC43OTFIMTcwTTE3MCA5Nkg5MC4zMjhNMTY5IDEzMy4yMUg1NS45NDQiIHN0eWxlPSJmaWxsOm5vbmU7c3Ryb2tlOiMwMDAwMDA7c3Ryb2tlLXdpZHRoOjEyO3N0cm9rZS1saW5lY2FwOnJvdW5kO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtZGFzaGFycmF5Om5vbmU7c3Ryb2tlLW9wYWNpdHk6MSIvPgogIDxwYXRoIGQ9Im03MyA4Mi04LjUgMjhtMCAwTDU2IDgybC04LjUgMjhNMzkgODJsOC41IDI4IiBzdHlsZT0iZm9udC12YXJpYXRpb24tc2V0dGluZ3M6bm9ybWFsO3ZlY3Rvci1lZmZlY3Q6bm9uZTtmaWxsOm5vbmU7ZmlsbC1vcGFjaXR5OjE7c3Ryb2tlOiMwMDAwMDA7c3Ryb2tlLXdpZHRoOjEyO3N0cm9rZS1saW5lY2FwOnJvdW5kO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2UtbWl0ZXJsaW1pdDo0O3N0cm9rZS1kYXNoYXJyYXk6bm9uZTtzdHJva2UtZGFzaG9mZnNldDowO3N0cm9rZS1vcGFjaXR5OjE7LWlua3NjYXBlLXN0cm9rZTpub25lO3N0b3AtY29sb3I6IzAwMCIvPgo8L3N2Zz4=) no-repeat center center;
    background-size: 16px !important;
    width: 20px !important;
    height: 22px !important;
    background-position: 3px 3px !important;
}
#e_imagen, #e_attachn {
    background: transparent url(data:image/svg+xml;base64,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) no-repeat center center !important;
    background-size: 14px !important;
    background-position: 0px 0px !important;
}
#e_url {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0wIDBoMTh2MThIMHoiIGZpbGw9Im5vbmUiLz4KICAgIDxwYXRoIGQ9Ik0xLjkgOWMwLTEuMTYuOTQtMi4xIDIuMS0yLjFoNFY1SDRDMS43OSA1IDAgNi43OSAwIDlzMS43OSA0IDQgNGg0di0xLjlINGMtMS4xNiAwLTIuMS0uOTQtMi4xLTIuMXpNMTQgNWgtNHYxLjloNGMxLjE2IDAgMi4xLjk0IDIuMSAyLjEgMCAxLjE2LS45NCAyLjEtMi4xIDIuMWgtNFYxM2g0YzIuMjEgMCA0LTEuNzkgNC00cy0xLjc5LTQtNC00em0tOCA1aDZWOEg2djJ6Ii8+Cjwvc3ZnPgo=) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;

    }
#e_cst1_sup {
    background: transparent url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyBmaWxsPSIjMDAwMDAwIiB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgNTYgNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHBhdGggZD0iTSA0My42NDQ1IDI4LjE0MDYgQyA0NC42OTkyIDI4LjE0MDYgNDUuMzU1NSAyNy40NjA5IDQ1LjM1NTUgMjYuNDc2NiBMIDQ1LjM1NTUgMTAuNDQ1MyBDIDQ1LjM1NTUgOS4yNzM1IDQ0LjY1MjQgOC41NzAzIDQzLjUwMzkgOC41NzAzIEMgNDIuNDcyNyA4LjU3MDMgNDEuOTU3MCA4Ljk0NTMgNDEuMjc3MyA5LjQzNzUgTCAzNy41MjczIDExLjk5MjIgQyAzNi44NzExIDEyLjQzNzUgMzYuNTg5OCAxMi44NTk0IDM2LjU4OTggMTMuMzc1MCBDIDM2LjU4OTggMTQuMTQ4NCAzNy4xNzU4IDE0LjczNDQgMzcuOTAyNCAxNC43MzQ0IEMgMzguMzcxMSAxNC43MzQ0IDM4LjY3NTggMTQuNTkzNyAzOS4xNDQ1IDE0LjI2NTcgTCA0MS44NjMzIDEyLjQzNzUgTCA0MS45MzM2IDEyLjQzNzUgTCA0MS45MzM2IDI2LjQ3NjYgQyA0MS45MzM2IDI3LjQ2MDkgNDIuNjM2NyAyOC4xNDA2IDQzLjY0NDUgMjguMTQwNiBaIE0gMTIuNzMwNSA0Ny40Mjk3IEMgMTMuOTAyNCA0Ny40Mjk3IDE0LjQ2NDkgNDYuOTYwOSAxNC45MzM2IDQ1LjY3MTkgTCAxNy45MzM2IDM3LjM3NTAgTCAzMS43NjE3IDM3LjM3NTAgTCAzNC43ODUxIDQ1LjY3MTkgQyAzNS4yMzA1IDQ2Ljk2MDkgMzUuODE2NCA0Ny40Mjk3IDM2Ljk4ODMgNDcuNDI5NyBDIDM4LjI1MzkgNDcuNDI5NyAzOS4wOTc2IDQ2LjY3OTcgMzkuMDk3NiA0NS41MDc4IEMgMzkuMDk3NiA0NS4xMDk0IDM5LjAyNzMgNDQuNzU3OCAzOC44Mzk4IDQ0LjI0MjIgTCAyNy44NDc2IDE0Ljk5MjIgQyAyNy4zMDg2IDEzLjUzOTEgMjYuMzQ3NiAxMi44MzU5IDI0Ljg0NzYgMTIuODM1OSBDIDIzLjM5NDUgMTIuODM1OSAyMi40MzM2IDEzLjUzOTEgMjEuOTE4MCAxNC45Njg4IEwgMTAuOTAyNCA0NC4yNjU2IEMgMTAuNzE0OSA0NC43ODEzIDEwLjY0NDUgNDUuMTMyOCAxMC42NDQ1IDQ1LjUzMTMgQyAxMC42NDQ1IDQ2LjcwMzEgMTEuNDQxNCA0Ny40Mjk3IDEyLjczMDUgNDcuNDI5NyBaIE0gMTkuMDgyMCAzMy43ODkxIEwgMjQuNzc3MyAxOC4wMTU3IEwgMjQuOTE4MCAxOC4wMTU3IEwgMzAuNTg5OCAzMy43ODkxIFoiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 16px !important;
    width: 20px !important;
    height: 22px !important;
    background-position: 2px 4px !important;
    }
#e_cst1_sub {
    background: transparent url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPHN2ZyBmaWxsPSIjMDAwMDAwIiB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgNTYgNTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHBhdGggZD0iTSA5LjQ4NDQgNDIuMzMyMCBDIDEwLjY1NjMgNDIuMzMyMCAxMS4yMTg4IDQxLjg2MzMgMTEuNjg3NSA0MC41NzQyIEwgMTQuNjg3NSAzMi4yNzc0IEwgMjguNTE1NiAzMi4yNzc0IEwgMzEuNTM5MCA0MC41NzQyIEMgMzEuOTg0NCA0MS44NjMzIDMyLjU3MDMgNDIuMzMyMCAzMy43NDIyIDQyLjMzMjAgQyAzNS4wMDc4IDQyLjMzMjAgMzUuODUxNiA0MS41ODIwIDM1Ljg1MTYgNDAuNDEwMiBDIDM1Ljg1MTYgNDAuMDExNyAzNS43ODEyIDM5LjY2MDIgMzUuNTkzNyAzOS4xNDQ1IEwgMjQuNjAxNiA5Ljg5NDUgQyAyNC4wNjI1IDguNDQxNCAyMy4xMDE2IDcuNzM4MyAyMS42MDE2IDcuNzM4MyBDIDIwLjE0ODQgNy43MzgzIDE5LjE4NzUgOC40NDE0IDE4LjY3MTkgOS44NzExIEwgNy42NTYzIDM5LjE2ODAgQyA3LjQ2ODggMzkuNjgzNiA3LjM5ODQgNDAuMDM1MiA3LjM5ODQgNDAuNDMzNiBDIDcuMzk4NCA0MS42MDU1IDguMTk1MyA0Mi4zMzIwIDkuNDg0NCA0Mi4zMzIwIFogTSAxNS44MzU5IDI4LjY5MTQgTCAyMS41MzEyIDEyLjkxODAgTCAyMS42NzE5IDEyLjkxODAgTCAyNy4zNDM3IDI4LjY5MTQgWiBNIDQ2Ljg5MDYgNDguMjYxNyBDIDQ3LjkyMTkgNDguMjYxNyA0OC42MDE2IDQ3LjU4MjEgNDguNjAxNiA0Ni41OTc3IEwgNDguNjAxNiAzMC41NjY0IEMgNDguNjAxNiAyOS4zOTQ1IDQ3Ljg5ODQgMjguNjkxNCA0Ni43MjY2IDI4LjY5MTQgQyA0NS42OTUzIDI4LjY5MTQgNDUuMjAzMSAyOS4wNjY0IDQ0LjUgMjkuNTU4NiBMIDQwLjc1IDMyLjExMzMgQyA0MC4wOTM3IDMyLjU4MjEgMzkuODM1OSAzMi45ODA1IDM5LjgzNTkgMzMuNDk2MSBDIDM5LjgzNTkgMzQuMjY5NSA0MC4zOTg0IDM0Ljg1NTUgNDEuMTI1MCAzNC44NTU1IEMgNDEuNTkzNyAzNC44NTU1IDQxLjg5ODQgMzQuNzE0OCA0Mi4zNjcyIDM0LjM4NjcgTCA0NS4wODU5IDMyLjU1ODYgTCA0NS4xNzk3IDMyLjU1ODYgTCA0NS4xNzk3IDQ2LjU5NzcgQyA0NS4xNzk3IDQ3LjU4MjEgNDUuODU5NCA0OC4yNjE3IDQ2Ljg5MDYgNDguMjYxNyBaIi8+Cjwvc3ZnPg==) no-repeat center center;
    background-size: 16px !important;
    width: 20px !important;
    height: 22px !important;
    background-position: 2px 4px !important;
    }
#fastposteditor #spanButtonPlaceholder {
background: transparent url(data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) no-repeat center center !important;
    background-size: 14px !important;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 6px !important;
    }
.b2r #e_sml {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE4IDE4Ij4KICAgIDxwYXRoIGQ9Ik0wIDBoMTh2MThIMHoiIGZpbGw9Im5vbmUiLz4KICAgIDxwYXRoIGQ9Ik02IDhjLjU1IDAgMS0uNDUgMS0xcy0uNDUtMS0xLTEtMSAuNDUtMSAxIC40NSAxIDEgMXptNiAwYy41NSAwIDEtLjQ1IDEtMXMtLjQ1LTEtMS0xLTEgLjQ1LTEgMSAuNDUgMSAxIDF6bS0zIDUuNWMyLjE0IDAgMy45Mi0xLjUgNC4zOC0zLjVINC42MmMuNDYgMiAyLjI0IDMuNSA0LjM4IDMuNXpNOSAxQzQuNTcgMSAxIDQuNTggMSA5czMuNTcgOCA4IDggOC0zLjU4IDgtOC0zLjU4LTgtOC04em0wIDE0LjVjLTMuNTkgMC02LjUtMi45MS02LjUtNi41UzUuNDEgMi41IDkgMi41czYuNSAyLjkxIDYuNSA2LjUtMi45MSA2LjUtNi41IDYuNXoiLz4KPC9zdmc+Cg==) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
.b2r #e_image {
    background: transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij4KICAgIDxwYXRoIGQ9Ik0wIDBoMjR2MjRIMHoiIGZpbGw9Im5vbmUiLz4KICAgIDxwYXRoIGQ9Ik0yMSAxOVY1YzAtMS4xLS45LTItMi0ySDVjLTEuMSAwLTIgLjktMiAydjE0YzAgMS4xLjkgMiAyIDJoMTRjMS4xIDAgMi0uOSAyLTJ6TTguNSAxMy41bDIuNSAzLjAxTDE0LjUgMTJsNC41IDZINWwzLjUtNC41eiIvPgo8L3N2Zz4K) no-repeat center center;
    background-size: 14px;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}
.b2r #e_attach {
    background: transparent url(data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) no-repeat center center;
    background-size: 14px !important;
    width: 20px !important;
    height: 22px !important;
    background-position: 4px 4px !important;
}

`;

const imageMap = {
  "static/image/filetype/zip.gif": "data:image/webp;base64,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",
  "static/image/filetype/rar.gif": "data:image/webp;base64,UklGRpAaAABXRUJQVlA4WAoAAAA0AAAAZwAAaQAASUNDUEgMAAAAAAxITGlubwIQAABtbnRyUkdCIFhZWiAHzgACAAkABgAxAABhY3NwTVNGVAAAAABJRUMgc1JHQgAAAAAAAAAAAAAAAAAA9tYAAQAAAADTLUhQICAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABFjcHJ0AAABUAAAADNkZXNjAAABhAAAAGx3dHB0AAAB8AAAABRia3B0AAACBAAAABRyWFlaAAACGAAAABRnWFlaAAACLAAAABRiWFlaAAACQAAAABRkbW5kAAACVAAAAHBkbWRkAAACxAAAAIh2dWVkAAADTAAAAIZ2aWV3AAAD1AAAACRsdW1pAAAD+AAAABRtZWFzAAAEDAAAACR0ZWNoAAAEMAAAAAxyVFJDAAAEPAAACAxnVFJDAAAEPAAACAxiVFJDAAAEPAAACAx0ZXh0AAAAAENvcHlyaWdodCAoYykgMTk5OCBIZXdsZXR0LVBhY2thcmQgQ29tcGFueQAAZGVzYwAAAAAAAAASc1JHQiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAABJzUkdCIElFQzYxOTY2LTIuMQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWFlaIAAAAAAAAPNRAAEAAAABFsxYWVogAAAAAAAAAAAAAAAAAAAAAFhZWiAAAAAAAABvogAAOPUAAAOQWFlaIAAAAAAAAGKZAAC3hQAAGNpYWVogAAAAAAAAJKAAAA+EAAC2z2Rlc2MAAAAAAAAAFklFQyBodHRwOi8vd3d3LmllYy5jaAAAAAAAAAAAAAAAFklFQyBodHRwOi8vd3d3LmllYy5jaAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABkZXNjAAAAAAAAAC5JRUMgNjE5NjYtMi4xIERlZmF1bHQgUkdCIGNvbG91ciBzcGFjZSAtIHNSR0IAAAAAAAAAAAAAAC5JRUMgNjE5NjYtMi4xIERlZmF1bHQgUkdCIGNvbG91ciBzcGFjZSAtIHNSR0IAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZGVzYwAAAAAAAAAsUmVmZXJlbmNlIFZpZXdpbmcgQ29uZGl0aW9uIGluIElFQzYxOTY2LTIuMQAAAAAAAAAAAAAALFJlZmVyZW5jZSBWaWV3aW5nIENvbmRpdGlvbiBpbiBJRUM2MTk2Ni0yLjEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHZpZXcAAAAAABOk/gAUXy4AEM8UAAPtzAAEEwsAA1yeAAAAAVhZWiAAAAAAAEwJVgBQAAAAVx/nbWVhcwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAo8AAAACc2lnIAAAAABDUlQgY3VydgAAAAAAAAQAAAAABQAKAA8AFAAZAB4AIwAoAC0AMgA3ADsAQABFAEoATwBUAFkAXgBjAGgAbQByAHcAfACBAIYAiwCQAJUAmgCfAKQAqQCuALIAtwC8AMEAxgDLANAA1QDbAOAA5QDrAPAA9gD7AQEBBwENARMBGQEfASUBKwEyATgBPgFFAUwBUgFZAWABZwFuAXUBfAGDAYsBkgGaAaEBqQGxAbkBwQHJAdEB2QHhAekB8gH6AgMCDAIUAh0CJgIvAjgCQQJLAlQCXQJnAnECegKEAo4CmAKiAqwCtgLBAssC1QLgAusC9QMAAwsDFgMhAy0DOANDA08DWgNmA3IDfgOKA5YDogOuA7oDxwPTA+AD7AP5BAYEEwQgBC0EOwRIBFUEYwRxBH4EjASaBKgEtgTEBNME4QTwBP4FDQUcBSsFOgVJBVgFZwV3BYYFlgWmBbUFxQXVBeUF9gYGBhYGJwY3BkgGWQZqBnsGjAadBq8GwAbRBuMG9QcHBxkHKwc9B08HYQd0B4YHmQesB78H0gflB/gICwgfCDIIRghaCG4IggiWCKoIvgjSCOcI+wkQCSUJOglPCWQJeQmPCaQJugnPCeUJ+woRCicKPQpUCmoKgQqYCq4KxQrcCvMLCwsiCzkLUQtpC4ALmAuwC8gL4Qv5DBIMKgxDDFwMdQyODKcMwAzZDPMNDQ0mDUANWg10DY4NqQ3DDd4N+A4TDi4OSQ5kDn8Omw62DtIO7g8JDyUPQQ9eD3oPlg+zD88P7BAJECYQQxBhEH4QmxC5ENcQ9RETETERTxFtEYwRqhHJEegSBxImEkUSZBKEEqMSwxLjEwMTIxNDE2MTgxOkE8UT5RQGFCcUSRRqFIsUrRTOFPAVEhU0FVYVeBWbFb0V4BYDFiYWSRZsFo8WshbWFvoXHRdBF2UXiReuF9IX9xgbGEAYZRiKGK8Y1Rj6GSAZRRlrGZEZtxndGgQaKhpRGncanhrFGuwbFBs7G2MbihuyG9ocAhwqHFIcexyjHMwc9R0eHUcdcB2ZHcMd7B4WHkAeah6UHr4e6R8THz4faR+UH78f6iAVIEEgbCCYIMQg8CEcIUghdSGhIc4h+yInIlUigiKvIt0jCiM4I2YjlCPCI/AkHyRNJHwkqyTaJQklOCVoJZclxyX3JicmVyaHJrcm6CcYJ0kneierJ9woDSg/KHEooijUKQYpOClrKZ0p0CoCKjUqaCqbKs8rAis2K2krnSvRLAUsOSxuLKIs1y0MLUEtdi2rLeEuFi5MLoIuty7uLyQvWi+RL8cv/jA1MGwwpDDbMRIxSjGCMbox8jIqMmMymzLUMw0zRjN/M7gz8TQrNGU0njTYNRM1TTWHNcI1/TY3NnI2rjbpNyQ3YDecN9c4FDhQOIw4yDkFOUI5fzm8Ofk6Njp0OrI67zstO2s7qjvoPCc8ZTykPOM9Ij1hPaE94D4gPmA+oD7gPyE/YT+iP+JAI0BkQKZA50EpQWpBrEHuQjBCckK1QvdDOkN9Q8BEA0RHRIpEzkUSRVVFmkXeRiJGZ0arRvBHNUd7R8BIBUhLSJFI10kdSWNJqUnwSjdKfUrESwxLU0uaS+JMKkxyTLpNAk1KTZNN3E4lTm5Ot08AT0lPk0/dUCdQcVC7UQZRUFGbUeZSMVJ8UsdTE1NfU6pT9lRCVI9U21UoVXVVwlYPVlxWqVb3V0RXklfgWC9YfVjLWRpZaVm4WgdaVlqmWvVbRVuVW+VcNVyGXNZdJ114XcleGl5sXr1fD19hX7NgBWBXYKpg/GFPYaJh9WJJYpxi8GNDY5dj62RAZJRk6WU9ZZJl52Y9ZpJm6Gc9Z5Nn6Wg/aJZo7GlDaZpp8WpIap9q92tPa6dr/2xXbK9tCG1gbbluEm5rbsRvHm94b9FwK3CGcOBxOnGVcfByS3KmcwFzXXO4dBR0cHTMdSh1hXXhdj52m3b4d1Z3s3gReG54zHkqeYl553pGeqV7BHtje8J8IXyBfOF9QX2hfgF+Yn7CfyN/hH/lgEeAqIEKgWuBzYIwgpKC9INXg7qEHYSAhOOFR4Wrhg6GcobXhzuHn4gEiGmIzokziZmJ/opkisqLMIuWi/yMY4zKjTGNmI3/jmaOzo82j56QBpBukNaRP5GokhGSepLjk02TtpQglIqU9JVflcmWNJaflwqXdZfgmEyYuJkkmZCZ/JpomtWbQpuvnByciZz3nWSd0p5Anq6fHZ+Ln/qgaaDYoUehtqImopajBqN2o+akVqTHpTilqaYapoum/adup+CoUqjEqTepqaocqo+rAqt1q+msXKzQrUStuK4trqGvFq+LsACwdbDqsWCx1rJLssKzOLOutCW0nLUTtYq2AbZ5tvC3aLfguFm40blKucK6O7q1uy67p7whvJu9Fb2Pvgq+hL7/v3q/9cBwwOzBZ8Hjwl/C28NYw9TEUcTOxUvFyMZGxsPHQce/yD3IvMk6ybnKOMq3yzbLtsw1zLXNNc21zjbOts83z7jQOdC60TzRvtI/0sHTRNPG1EnUy9VO1dHWVdbY11zX4Nhk2OjZbNnx2nba+9uA3AXcit0Q3ZbeHN6i3ynfr+A24L3hROHM4lPi2+Nj4+vkc+T85YTmDeaW5x/nqegy6LzpRunQ6lvq5etw6/vshu0R7ZzuKO6070DvzPBY8OXxcvH/8ozzGfOn9DT0wvVQ9d72bfb794r4Gfio+Tj5x/pX+uf7d/wH/Jj9Kf26/kv+3P9t//9BTFBIHgMAAAEZMmkbsr3zr3giIvofI/mhBknXtp2R9AblGtu2bds2t8Zf4GpmZdu2bdue6R4b5cr3Db4vna55x3oTMQEM3DZS5O7uMeMbIFmNl4ebg05FCgJSa7ImKRwBqRXXRBxDcTFhIQEgccmtpHKKycdLxwEGcOAzYUhqTchVno84YgxxEe89d99151yShEm97BppJycD+NJhHjlpve2SpbKvqxaiAI7EXLfAYkmsc2uslwiAJzGnjLdZkEVZ9VIUwJUnZpriAUvy6YsB2BK2xFg3Wak9RAF8MSw2Usqy6SACEMYyVS4p8WskJ4AzQV2lY05TUmMAa4apRGcqsw4AWvkMkJUZhwrywItaqcPFRFrtEWIe/aVnMr2YnDCjOsrTSZwaAKj5tOIkn9IacKN6/ESK/JyQK6Q4hagEgJxPGZKSsCNbkh96BagEumzQy8FD/KVyQS89P5ECPQ8fcU0Mejo38e3NoecgiUNPoxOoAHoKjbhqBj2V5qtpxGVtDuDP1+ZrGJRG/+st3Penoe/NAEc/bKN/SS/2dS3P0Q/bd5y+lvUxhB4DRF9BI5Y4/I0BCGIMmJmvNyIKP3G9IQHohTGi19B7SyMOPILeE37iLEnQu8dB3OgN6N2kEV31FnIht6mAENtGLkmYQmxeOAK54zKSt7tXAGoRh7hJYs46BrUz4nQS5ok1QhCL2SgDBZASsM8eiB0W5mD2wUiyxANoPbdVRgoT3HuHLBSCVNQqOTkAsxieWGUdA0rcem/5KIBpVGlUMUJzKoR1r3FcFCkgTXpVDNKUE7rv6l4THGHhnOnSK6OftjJA9rnbYo4zXgEKCyWt/FrqoSgdHoY7llrPfiPOCtL4ZFZKG83lZOPAPLbTOuc9E2D1nla4pJVZMXXVUkQ6ii9+271zyyH7XfHMWxEcsByVRxppZZJfMYXllVk6LjqNihQggYfKAY4hiol457kkt1x111NvvRfCAOJAKnqcPGw3D9vLx3Zx0AklodUQRSEBQXZIhG0TAxQcSGVUOttBY9sKcYyCSwwFJ4wgxkAUF5PgP2sVIErksHRtrK+PAFZQOCBWBwAAECUAnQEqaABqAD5JHoxEIqGhmSu2eCgEhLE2nK0WF/LV8B+VXRi7/eFeemN/18fnvJV6QD9MuoV5gP2O9WP8gPd96B/9g/s3ogey5+zPsAfq76an7Y/C7+4uCKVtHg3v1/D3J3wBYAPqhxC9yZ6Sd8IZI0DfW/sHfrR1sFFNfvcvPfMQ3OPcwrxvxrtycD/CtFE9Nl1zdCxNBO7lHayxBBU/Bj7gNDLFFKJvMlcgWeV58JnE5TM1NCD1EReFMGvYK7En9sVnU5JTgi+uGa/SYv3/GdbcHBTZgRFeONA6CgpYIug7PAO5s8CJvFDRDSCEsu0ABaJ+ROna4Tr5N+aHBim1yvHueqJMG5a7dXUtHTHkWq6fHuf2yrOGLvdMNYXrd3I2oxLevmv6IhugwfvvE+AA/v4+7uo/W9lqRZdY/sIgtc/kdjBegP9CvuYgqkxfKwiw+ulb2tj5fOJGCz/5OCYXHjb4eX31u+9K+BxFR8G7h3/HP+A6j2AmunUI1plD+uQgzn5Q4k7yr2QQ8WRh0is69XLmqzuedS351oZ5Mb3f8B/84tx8VUFzhZnnElTGO1HgVzo47VjMy4X0aFHt9HSYiX/e039MrXVzzuc0HRTpS3vSC2evjhH0NRto02xkNcwcytGxzEKvaEO+GJ6sXgn4aWV340PowcTAObpwwlX/hfv+oIO8/mWzdEpz9WfDunbqq6iUrOIpCxXJ8I08CleyBCpXBkRQI3hEgzoqnsCDRBFbmqXfsh0K0lDRBrs4V/PkFvYkHMZW3PBV20f6PY0M375L9qNoeXoRFvJtV0Uq9LL6yHoh46o5wLf8UB3wcOfqG/29E0qepyrnGKvxg3f4nYa5+v1FKQKMGSn7Q5wlBIn2j+mxHAql590QRf6/oOzB4spHqUamnGt9nQczbW9xkGa+lH4yndHp1sKt1joV0CF8s0AMyzky2yua+kzSjrFrqPAjZ1C+zuYNfRLcQJzST0GZqkcGCyn7dH/CFm4i4rQpQuxaLoLjdfteyHYRNJK2VtLQT2IkSX8MV2uxs+EAuNjYSv3OCRRHP3/Rkta8CrWt/8XgQxixk6BdsxXdrrr1HW4TBy91/apuHGcHg3Vb6FnHHLQPc1RmqsvEnaNOK8jZFwW2ot5seSEWgk8TCyazfL30eG9e19mxw4nTNl7JW2HjzdMbNq3yZ///hor5lt7dBZa/whrTsv3ZFH/8WjBPy2XshH9/tfsPv8Ts5znZW330nz+cZSWB31E+4ELgl8laBx5B/BJXshz6Nd+xov+n8MWIQVmmKe7lkRk+HXA+Lv+uluOOeu/7dcjcn/+ALIT7I7VB2p1/6bz7vDVDh5jo6YjkKdvqR0YZ20Q6/Bln1zTf7XIF+njJt+h3gEr0aZNHO23wYVPYdDfiiF1WJeOnLpwm1xyx4MAcQjlDUBEdnc5+IsVF4JQMFFvdHCPz+qHLVuBezftyXgljAuRbR1zrcZ5sTs6uVI5+C33GscgX6HDc57EPivHA199X5Ow4HoHjlF7pNf63nm3BAAMqvQlBAIYEkpmODbLV/xHM3qKR4ABvW2kUnqjp+YiOMQQVF1lk2G0zL73Q6isdjhuLnwSfH4PoU7Xx9dP1CYyh1mOucEyPj1TXiM74y3EM6uZybqjtVdsnd8urtcYB8nlF99qeTYCyPOdA/+82pzRUDsd5ydiTbIKcefsQ9QN+s6Sbegq0NkV3j6R1T0FCrtVwDhsvRmKlrQID5SIepYO+P+Q2jr0l152m+EfSggtZDZGu7JcptA9Xy4c/zxNqlILBEV5nbFHGVrSyRPHCUsWikughTeReNGf+idlf85sT59wOOJ9z0XpnTm26hZ5+YccNKzHtP/uOOuQepkkt/F/p1WTEyi94M0f7AN6l+WHTHa0sf2wgBK3Zb+9DyYZU/14QWKO3aHiH9dIzhta+WV2VEmbhOGDs4YnYzmgok3cZzLo8or6C/NJd4pLC75g+mati1K8uANgeIIcuJtJU6C0We6MGMf86HIpfr4xmhLFciILW6kPDJcXQ3K4AG4fL9a6IOvl+5K+pTXrq8asufgEPt/KiG7n+nY7oy7mLz/r9eovhIyNNV+SDhpDwYTWzbv6Fa9f/0yXz2xTmjZlmDad6wQCZFZ3wUBcYG4sfkL92AHeb2pGn1felA9TfP/oQcZDr8wmp+jp3ZEEdxRL/pZbORijfAZDm1shQVbrtm065jg9I6LVkTbmRw36AXzNKnBub05x8iseZftIvz9dtDPp3qzWSHTvU2jJqmBmgeM8oZtJAAm0FYNCQ9+a/j58MtWb3Van3fhX7zV0YGJrfxXXQLADtJ1rQ0/TdRAu//CMDhkKMGNd2xCeWUL34V7Ye/wugYcWmn9gPu2XrtLS4gDShA4TmgKc1KEEpcHw0qSt6UB0sdtFZtl1VlZQS+INXfw6k5AQ9aiyu78WfeUKuHsR9Qm061j9Lgy7qKf84Sz3KQI8tZ5v39FMElCMEv63eb/8rj8CgAAAAWE1QIJ0DAAA8eDp4bXBtZXRhIHhtbG5zOng9ImFkb2JlOm5zOm1ldGEvIiB4OnhtcHRrPSJYTVAgQ29yZSA2LjAuMCI+CiAgIDxyZGY6UkRGIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+CiAgICAgIDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PSIiCiAgICAgICAgICAgIHhtbG5zOmV4aWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20vZXhpZi8xLjAvIgogICAgICAgICAgICB4bWxuczp0aWZmPSJodHRwOi8vbnMuYWRvYmUuY29tL3RpZmYvMS4wLyIKICAgICAgICAgICAgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIj4KICAgICAgICAgPGV4aWY6UGl4ZWxZRGltZW5zaW9uPjEwNjwvZXhpZjpQaXhlbFlEaW1lbnNpb24+CiAgICAgICAgIDxleGlmOlBpeGVsWERpbWVuc2lvbj4xMDQ8L2V4aWY6UGl4ZWxYRGltZW5zaW9uPgogICAgICAgICA8dGlmZjpSZXNvbHV0aW9uVW5pdD4yPC90aWZmOlJlc29sdXRpb25Vbml0PgogICAgICAgICA8dGlmZjpYUmVzb2x1dGlvbj43MjAwMDAvMTAwMDA8L3RpZmY6WFJlc29sdXRpb24+CiAgICAgICAgIDx0aWZmOk9yaWVudGF0aW9uPjE8L3RpZmY6T3JpZW50YXRpb24+CiAgICAgICAgIDx0aWZmOllSZXNvbHV0aW9uPjcyMDAwMC8xMDAwMDwvdGlmZjpZUmVzb2x1dGlvbj4KICAgICAgICAgPHhtcDpNZXRhZGF0YURhdGU+MjAyNS0wNS0zMVQwMToxMjo1MSswODowMDwveG1wOk1ldGFkYXRhRGF0ZT4KICAgICAgICAgPHhtcDpDcmVhdG9yVG9vbD5QaXhlbG1hdG9yIFBybyAzLjYuMTc8L3htcDpDcmVhdG9yVG9vbD4KICAgICAgICAgPHhtcDpDcmVhdGVEYXRlPjIwMjUtMDUtMzFUMDE6MTE6MjMrMDg6MDA8L3htcDpDcmVhdGVEYXRlPgogICAgICA8L3JkZjpEZXNjcmlwdGlvbj4KICAgPC9yZGY6UkRGPgo8L3g6eG1wbWV0YT4KAA==",
  "static/image/filetype/text.gif": "data:image/webp;base64,UklGRioVAABXRUJQVlA4WAoAAAA0AAAAYwAAYwAASUNDUMgHAAAAAAfIYXBwbAIgAABtbnRyUkdCIFhZWiAH2QACABkACwAaAAthY3NwQVBQTAAAAABhcHBsAAAAAAAAAAAAAAAAAAAAAAAA9tYAAQAAAADTLWFwcGwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAtkZXNjAAABCAAAAG9kc2NtAAABeAAABYpjcHJ0AAAHBAAAADh3dHB0AAAHPAAAABRyWFlaAAAHUAAAABRnWFlaAAAHZAAAABRiWFlaAAAHeAAAABRyVFJDAAAHjAAAAA5jaGFkAAAHnAAAACxiVFJDAAAHjAAAAA5nVFJDAAAHjAAAAA5kZXNjAAAAAAAAABRHZW5lcmljIFJHQiBQcm9maWxlAAAAAAAAAAAAAAAUR2VuZXJpYyBSR0IgUHJvZmlsZQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAbWx1YwAAAAAAAAAfAAAADHNrU0sAAAAoAAABhGRhREsAAAAkAAABrGNhRVMAAAAkAAAB0HZpVk4AAAAkAAAB9HB0QlIAAAAmAAACGHVrVUEAAAAqAAACPmZyRlUAAAAoAAACaGh1SFUAAAAoAAACkHpoVFcAAAASAAACuGtvS1IAAAAWAAACym5iTk8AAAAmAAAC4GNzQ1oAAAAiAAADBmhlSUwAAAAeAAADKHJvUk8AAAAkAAADRmRlREUAAAAsAAADaml0SVQAAAAoAAADlnN2U0UAAAAmAAAC4HpoQ04AAAASAAADvmphSlAAAAAaAAAD0GVsR1IAAAAiAAAD6nB0UE8AAAAmAAAEDG5sTkwAAAAoAAAEMmVzRVMAAAAmAAAEDHRoVEgAAAAkAAAEWnRyVFIAAAAiAAAEfmZpRkkAAAAoAAAEoGhySFIAAAAoAAAEyHBsUEwAAAAsAAAE8HJ1UlUAAAAiAAAFHGVuVVMAAAAmAAAFPmFyRUcAAAAmAAAFZABWAWEAZQBvAGIAZQBjAG4A/QAgAFIARwBCACAAcAByAG8AZgBpAGwARwBlAG4AZQByAGUAbAAgAFIARwBCAC0AcAByAG8AZgBpAGwAUABlAHIAZgBpAGwAIABSAEcAQgAgAGcAZQBuAOgAcgBpAGMAQx6lAHUAIABoAOwAbgBoACAAUgBHAEIAIABDAGgAdQBuAGcAUABlAHIAZgBpAGwAIABSAEcAQgAgAEcAZQBuAOkAcgBpAGMAbwQXBDAEMwQwBDsETAQ9BDgEOQAgBD8EQAQ+BEQEMAQ5BDsAIABSAEcAQgBQAHIAbwBmAGkAbAAgAGcA6QBuAOkAcgBpAHEAdQBlACAAUgBWAEIAwQBsAHQAYQBsAOEAbgBvAHMAIABSAEcAQgAgAHAAcgBvAGYAaQBskBp1KABSAEcAQoJyX2ljz4/wx3y8GAAgAFIARwBCACDVBLhc0wzHfABHAGUAbgBlAHIAaQBzAGsAIABSAEcAQgAtAHAAcgBvAGYAaQBsAE8AYgBlAGMAbgD9ACAAUgBHAEIAIABwAHIAbwBmAGkAbAXkBegF1QXkBdkF3AAgAFIARwBCACAF2wXcBdwF2QBQAHIAbwBmAGkAbAAgAFIARwBCACAAZwBlAG4AZQByAGkAYwBBAGwAbABnAGUAbQBlAGkAbgBlAHMAIABSAEcAQgAtAFAAcgBvAGYAaQBsAFAAcgBvAGYAaQBsAG8AIABSAEcAQgAgAGcAZQBuAGUAcgBpAGMAb2ZukBoAUgBHAEJjz4/wZYdO9k4AgiwAIABSAEcAQgAgMNcw7TDVMKEwpDDrA5MDtQO9A7kDugPMACADwAPBA78DxgOvA7sAIABSAEcAQgBQAGUAcgBmAGkAbAAgAFIARwBCACAAZwBlAG4A6QByAGkAYwBvAEEAbABnAGUAbQBlAGUAbgAgAFIARwBCAC0AcAByAG8AZgBpAGUAbA5CDhsOIw5EDh8OJQ5MACAAUgBHAEIAIA4XDjEOSA4nDkQOGwBHAGUAbgBlAGwAIABSAEcAQgAgAFAAcgBvAGYAaQBsAGkAWQBsAGUAaQBuAGUAbgAgAFIARwBCAC0AcAByAG8AZgBpAGkAbABpAEcAZQBuAGUAcgBpAQ0AawBpACAAUgBHAEIAIABwAHIAbwBmAGkAbABVAG4AaQB3AGUAcgBzAGEAbABuAHkAIABwAHIAbwBmAGkAbAAgAFIARwBCBB4EMQRJBDgEOQAgBD8EQAQ+BEQEOAQ7BEwAIABSAEcAQgBHAGUAbgBlAHIAaQBjACAAUgBHAEIAIABQAHIAbwBmAGkAbABlBkUGRAZBACAGKgY5BjEGSgZBACAAUgBHAEIAIAYnBkQGOQYnBkUAAHRleHQAAAAAQ29weXJpZ2h0IDIwMDcgQXBwbGUgSW5jLiwgYWxsIHJpZ2h0cyByZXNlcnZlZC4AWFlaIAAAAAAAAPNSAAEAAAABFs9YWVogAAAAAAAAdE0AAD3uAAAD0FhZWiAAAAAAAABadQAArHMAABc0WFlaIAAAAAAAACgaAAAVnwAAuDZjdXJ2AAAAAAAAAAEBzQAAc2YzMgAAAAAAAQxCAAAF3v//8yYAAAeSAAD9kf//+6L///2jAAAD3AAAwGxBTFBIyAEAAAGQbNu2aVd7nxubZZdtq5hUzVJSwrNt88S2cdVeOX/xbFsfsEtrH80YLSImgG2KeofGFDYdvnH7ntrZ2dXT2z84PDo+OTO3aHdL2hfnZ6bGR4cH+3t7ujo71Xu3bx5rKokP8zEHT7rwTlj6y40MbljzSwHw/SFjkr4JkL8L9FN6BVC7TSf/xwLqu1Bd/J8JsO9DdVAeC7gfPLT1CcCPNCUJyKVavmP6y+VaBOhTUvwlqq+KTLKAnS9zAVeXzFtcfzjlLYD7U6HIIqgoZElUHrJqqhHZPmofslvUNWQPqZvIBqg7yCao+8iWqC5kDmocmZOyI3NT7j1L2FeFxVVhdlWYWhXGV4WRVWFoVehfFXpXhe5VoXNVUFeFh8hc1ANkzlXBTt1HtkTdQTZB3UTWT11DplL7kN2iGpHtp/KR1VDRyJKoUGQRlDcyf4q9x/WPS1zC1cMkU3AVyvDXqL4rMqwV1Xkm/xPTP0VDBqYypnUS0QrTbHuJ56unNhb0Gs2XSKZn0GssXyOZvrZZJCueTPe83yj+lzNDD39E8P2Cwgzm2fd/Wetfb5HCTOkXnlDRdvNh//ii3ek2pdOxNDGg3tpfkxzhz9lmKFZQOCC+AgAAcBEAnQEqZABkAD5RIo1Eo6IhFEzucDgFBLGLwBV2uXCmMe+vRt5QMQagD8gMcB+pvrj9EB5wG9JexZ+xvpEOGzvBaLe4CLA5M35085w5Vkpj1Ug4+3ovcPDdRiqFk6m/pwJP9VizSIW8tlu1woT2mRkZ7HpJik1dulBiXcnfRA+b9hu8etmoi4aMGB/Sh3O4MD9wAAD+64P//mm//8kz///L3CCSxMnoc/fVdvPC0H6ZUDnr/3+xQe//X/8an4Q0F8JCRpPH2krav0jH3ie9Kw9Vybk+19x13dTawa6fwhAU/gk8s2bqc2TDIDk82XhQ47LKVRsQB+R5HMKjl1Xq2Anf8bVIx+9tihgFQ+//3pPvWZtdhxiNtvu/+3Gv2n/f/bNx33Hmx7SP+6TyH+JzbJKiSsNdMHW//2lJuuZlqMRSkZii/f8zv+rvqE5/oOJoTdnCSaX7hG0I0FxXalX56DWP8kCUQJ6rVGnc6TYiYpYQmrufrK/9vHr3luqeOzmSF374aeVnkOGq9b77X8SayDWzxNTaZverx/uta0Jave3JKW/YmtjPp069wntJWspAqYMwg7/hGKPpmyMo/MCXIHZnkMClc4o8pc9jJAm+OfkIG0Ex2sV8B6c20+aSdr4ntIMHb/jPJERPpcQX+h/m8PRL6R53s1Qt85wVPETm4u+3mm5UDLBdS9P3+n8GUDXpB/vdZKv1GuFpjUZFLOk5zjOEysI+f8togMsg97uEFIN5uQsirwNjp4+aS24859f0YvgDtumFtHDWiBzbMGrwa59OxrxQVngxttMT4C+LtuyWCBik0z4yHwhbdzeO+PqFE6mnAtjoav/Hky7kuLTB+DeAM+fweTsroU/+h+5pNYYmgSZqByzecI/wWMWBUcSQ2XGJMSUTCAVcFDlHvX7Tzeqk4LWam2iyAAAAAAAAWE1QIKYIAAA8eDp4bXBtZXRhIHhtbG5zOng9ImFkb2JlOm5zOm1ldGEvIiB4OnhtcHRrPSJYTVAgQ29yZSA2LjAuMCI+CiAgIDxyZGY6UkRGIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+CiAgICAgIDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PSIiCiAgICAgICAgICAgIHhtbG5zOlBpeGVsbWF0b3JUZWFtPSJodHRwOi8vd3d3LnBpeGVsbWF0b3IuY29tL3htcC8xLjAvbmFtZXNwYWNlIgogICAgICAgICAgICB4bWxuczp0aWZmPSJodHRwOi8vbnMuYWRvYmUuY29tL3RpZmYvMS4wLyIKICAgICAgICAgICAgeG1sbnM6ZXhpZj0iaHR0cDovL25zLmFkb2JlLmNvbS9leGlmLzEuMC8iCiAgICAgICAgICAgIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyI+CiAgICAgICAgIDxQaXhlbG1hdG9yVGVhbTpTaWRlY2FyRGF0YVZlcnNpb24+MzwvUGl4ZWxtYXRvclRlYW06U2lkZWNhckRhdGFWZXJzaW9uPgogICAgICAgICA8UGl4ZWxtYXRvclRlYW06U2lkZWNhcldyaXRlckFwcGxpY2F0aW9uPnBpeGVsbWF0b3JQcm88L1BpeGVsbWF0b3JUZWFtOlNpZGVjYXJXcml0ZXJBcHBsaWNhdGlvbj4KICAgICAgICAgPFBpeGVsbWF0b3JUZWFtOlNpZGVjYXJFbmFibGVkPkZhbHNlPC9QaXhlbG1hdG9yVGVhbTpTaWRlY2FyRW5hYmxlZD4KICAgICAgICAgPFBpeGVsbWF0b3JUZWFtOlNpZGVjYXJXcml0ZXJPUz4xNS41LjA8L1BpeGVsbWF0b3JUZWFtOlNpZGVjYXJXcml0ZXJPUz4KICAgICAgICAgPFBpeGVsbWF0b3JUZWFtOlNpZGVjYXJXcml0ZXJQbGF0Zm9ybT5tYWNPUzwvUGl4ZWxtYXRvclRlYW06U2lkZWNhcldyaXRlclBsYXRmb3JtPgogICAgICAgICA8UGl4ZWxtYXRvclRlYW06U2lkZWNhcldyaXRlckRldmljZT5NYWMxMywxPC9QaXhlbG1hdG9yVGVhbTpTaWRlY2FyV3JpdGVyRGV2aWNlPgogICAgICAgICA8UGl4ZWxtYXRvclRlYW06U2lkZWNhclVUST5jb20ucGl4ZWxtYXRvcnRlYW0ucGl4ZWxtYXRvci5kb2N1bWVudC1wcm8tc2lkZWNhci5iaW5hcnk8L1BpeGVsbWF0b3JUZWFtOlNpZGVjYXJVVEk+CiAgICAgICAgIDxQaXhlbG1hdG9yVGVhbTpTaWRlY2FyV3JpdGVyUHJvY2Vzc0lEPjcyNTgxPC9QaXhlbG1hdG9yVGVhbTpTaWRlY2FyV3JpdGVyUHJvY2Vzc0lEPgogICAgICAgICA8UGl4ZWxtYXRvclRlYW06U2lkZWNhclZlcnNpb24+MjwvUGl4ZWxtYXRvclRlYW06U2lkZWNhclZlcnNpb24+CiAgICAgICAgIDxQaXhlbG1hdG9yVGVhbTpTaWRlY2FyV3JpdGVyQnVpbGQ+YTYyZDA5NzwvUGl4ZWxtYXRvclRlYW06U2lkZWNhcldyaXRlckJ1aWxkPgogICAgICAgICA8UGl4ZWxtYXRvclRlYW06U2lkZWNhcklkZW50aWZpZXI+RjQyQzA3MjQtNDNBMS00MDE1LTkyRDktRTUyQ0IyMzkxMjgwPC9QaXhlbG1hdG9yVGVhbTpTaWRlY2FySWRlbnRpZmllcj4KICAgICAgICAgPFBpeGVsbWF0b3JUZWFtOlNpZGVjYXJMb2NhdGlvbj5sb2NhbDwvUGl4ZWxtYXRvclRlYW06U2lkZWNhckxvY2F0aW9uPgogICAgICAgICA8UGl4ZWxtYXRvclRlYW06U2lkZWNhclNob3J0SGFzaD5GNDJDMDcyNDwvUGl4ZWxtYXRvclRlYW06U2lkZWNhclNob3J0SGFzaD4KICAgICAgICAgPFBpeGVsbWF0b3JUZWFtOlNpZGVjYXJXcml0ZXJWZXJzaW9uPjMuNi4xNzwvUGl4ZWxtYXRvclRlYW06U2lkZWNhcldyaXRlclZlcnNpb24+CiAgICAgICAgIDx0aWZmOlJlc29sdXRpb25Vbml0PjI8L3RpZmY6UmVzb2x1dGlvblVuaXQ+CiAgICAgICAgIDx0aWZmOk9yaWVudGF0aW9uPjE8L3RpZmY6T3JpZW50YXRpb24+CiAgICAgICAgIDx0aWZmOllSZXNvbHV0aW9uPjcyMDAwMC8xMDAwMDwvdGlmZjpZUmVzb2x1dGlvbj4KICAgICAgICAgPHRpZmY6WFJlc29sdXRpb24+NzIwMDAwLzEwMDAwPC90aWZmOlhSZXNvbHV0aW9uPgogICAgICAgICA8ZXhpZjpQaXhlbFlEaW1lbnNpb24+MTAwPC9leGlmOlBpeGVsWURpbWVuc2lvbj4KICAgICAgICAgPGV4aWY6UGl4ZWxYRGltZW5zaW9uPjEwMDwvZXhpZjpQaXhlbFhEaW1lbnNpb24+CiAgICAgICAgIDx4bXA6TWV0YWRhdGFEYXRlPjIwMjUtMDUtMzFUMTI6MjY6MDUrMDg6MDA8L3htcDpNZXRhZGF0YURhdGU+CiAgICAgICAgIDx4bXA6Q3JlYXRvclRvb2w+UGl4ZWxtYXRvciBQcm8gMy42LjE3PC94bXA6Q3JlYXRvclRvb2w+CiAgICAgIDwvcmRmOkRlc2NyaXB0aW9uPgogICA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgo=",
  "static/image/filetype/torrent.gif": "data:image/webp;base64,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",
  "static/image/common/settop.png": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDE2IDE2Ij48cGF0aCBmaWxsPSIjZWE1ODBjIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xMS4yOCAxLjIyYS43NS43NSAwIDAgMC0xLjI2LjdMNi42OSA1LjI1SDQuMjA2Yy0xLjExNCAwLTEuNjcxIDEuMzQ2LS44ODQgMi4xMzRsMS45MTEgMS45MTFsLTMuNzIgNC4xMzVBMiAyIDAgMCAwIDEgMTQuNzY4VjE1aC4yMzNhMiAyIDAgMCAwIDEuMzM3LS41MTNsNC4xMzUtMy43MjFsMS45MTEgMS45MWMuNzg4Ljc4OCAyLjEzNC4yMyAyLjEzNC0uODgzVjkuMzFsMy4zMy0zLjMzYS43NS43NSAwIDAgMCAuNy0xLjI2MWwtLjYwMy0uNjA0bC0yLjI5My0yLjI5M3oiIGNsaXAtcnVsZT0iZXZlbm9kZCIvPjwvc3ZnPg==",
  "static/image/stamp/001.small.gif": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgdmlld0JveD0iMCAwIDEyOCAxMjgiPjxwYXRoIGZpbGw9IiNmY2MyMWIiIGQ9Im04MS40OSA3NS4zbDYuOTEgMjguMTVMNjQgODhsLTI0LjM5IDE1LjQ1bDYuOTItMjguMTVsLTIyLTE4LjI2bDI4LjY3LTIuM0w2NCAyOC4zNmwxMC44MSAyNi4zOGwyOC42NiAyLjN6Ii8+PHBhdGggZmlsbD0iI2Y3OTMyOSIgZD0iTTEyNy4xOSA0OS41MmEzLjI3IDMuMjcgMCAwIDAtMi44NC0yLjI3bC00MS42NC0zLjMyTDY3LjAzIDUuNjJBMy4yNyAzLjI3IDAgMCAwIDY0IDMuNmMtMS4zMiAwLTIuNTEuNzktMy4wMiAyLjAyTDQ1LjMgNDMuOTNMMy42NSA0Ny4yNUEzLjI5IDMuMjkgMCAwIDAgLjggNDkuNTJjLS4zOCAxLjI3LjAxIDIuNjUgMS4wMyAzLjVsMzEuOSAyNi40OWwtMTAuMDMgNDAuODVjLS4zMiAxLjI5LjE4IDIuNjUgMS4yNiAzLjQyYy41Ny40MiAxLjIzLjYyIDEuOS42MmMuNjEgMCAxLjIyLS4xNyAxLjc1LS41TDY0IDEwMS41bDM1LjM5IDIyLjM5YzEuMTMuNzIgMi41Ny42NyAzLjY1LS4xMWEzLjI0NSAzLjI0NSAwIDAgMCAxLjI2LTMuNDJMOTQuMjcgNzkuNTFsMzEuOS0yNi41YTMuMjMgMy4yMyAwIDAgMCAxLjAyLTMuNDlNODEuNDkgNzUuM2w2LjkyIDI4LjE1TDY0IDg4bC0yNC40IDE1LjQ0bDYuOTItMjguMTVsLTIyLTE4LjI2bDI4LjY3LTIuMjlMNjQgMjguMzZsMTAuODEgMjYuMzhsMjguNjYgMi4yOXoiLz48L3N2Zz4=",
  "static/image/common/digest_1.gif": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgdmlld0JveD0iMCAwIDEyOCAxMjgiPjxwYXRoIGZpbGw9IiNmY2MyMWIiIGQ9Im04MS40OSA3NS4zbDYuOTEgMjguMTVMNjQgODhsLTI0LjM5IDE1LjQ1bDYuOTItMjguMTVsLTIyLTE4LjI2bDI4LjY3LTIuM0w2NCAyOC4zNmwxMC44MSAyNi4zOGwyOC42NiAyLjN6Ii8+PHBhdGggZmlsbD0iI2Y3OTMyOSIgZD0iTTEyNy4xOSA0OS41MmEzLjI3IDMuMjcgMCAwIDAtMi44NC0yLjI3bC00MS42NC0zLjMyTDY3LjAzIDUuNjJBMy4yNyAzLjI3IDAgMCAwIDY0IDMuNmMtMS4zMiAwLTIuNTEuNzktMy4wMiAyLjAyTDQ1LjMgNDMuOTNMMy42NSA0Ny4yNUEzLjI5IDMuMjkgMCAwIDAgLjggNDkuNTJjLS4zOCAxLjI3LjAxIDIuNjUgMS4wMyAzLjVsMzEuOSAyNi40OWwtMTAuMDMgNDAuODVjLS4zMiAxLjI5LjE4IDIuNjUgMS4yNiAzLjQyYy41Ny40MiAxLjIzLjYyIDEuOS42MmMuNjEgMCAxLjIyLS4xNyAxLjc1LS41TDY0IDEwMS41bDM1LjM5IDIyLjM5YzEuMTMuNzIgMi41Ny42NyAzLjY1LS4xMWEzLjI0NSAzLjI0NSAwIDAgMCAxLjI2LTMuNDJMOTQuMjcgNzkuNTFsMzEuOS0yNi41YTMuMjMgMy4yMyAwIDAgMCAxLjAyLTMuNDlNODEuNDkgNzUuM2w2LjkyIDI4LjE1TDY0IDg4bC0yNC40IDE1LjQ0bDYuOTItMjguMTVsLTIyLTE4LjI2bDI4LjY3LTIuMjlMNjQgMjguMzZsMTAuODEgMjYuMzhsMjguNjYgMi4yOXoiLz48L3N2Zz4=",
  "static/image/common/systempm.png": "data:image/svg+xml;base64,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",
  "static/image/stamp/011.small.gif": "data:image/svg+xml;base64,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",
};
// 使用全局变量跟踪样式是否已注入 - 确保只注入一次
let stylesInjected = false;

// 样式注入函数，确保样式只被注入一次
function injectStyles(styles, id) {
  try {
    // 全局检查样式是否已注入，无论通过什么方式
    if (stylesInjected) {
      return;
    }

    // 检查DOM中是否存在样式元素
    if (document.getElementById(id)) {
      stylesInjected = true;
      return;
    }

    // 直接创建style元素注入（最可靠的方法）
    const styleEl = document.createElement("style");
    styleEl.id = id;
    styleEl.textContent = styles;
    styleEl.type = "text/css";

    // 插入样式到head或documentElement
    if (document.head) {
      document.head.appendChild(styleEl);
    } else if (document.documentElement) {
      // 如果head不存在，则先添加到html元素
      document.documentElement.appendChild(styleEl);

      // 设置观察器，一旦head存在就移动过去
      const docObserver = new MutationObserver(() => {
        if (document.head) {
          if (styleEl.parentNode !== document.head) {
            document.head.appendChild(styleEl);
          }
          docObserver.disconnect();
        }
      });
      docObserver.observe(document.documentElement, { childList: true });
    }

    // 标记样式已注入
    stylesInjected = true;
  } catch (error) {
    // 样式应用失败，静默处理
  }
}

// DOM 操作函数
function initializeDOMHandlers() {
  // 替换图片图标
  document.querySelectorAll("img").forEach(function (img) {
    for (const [key, newSrc] of Object.entries(imageMap)) {
      if (img.src.includes(key)) {
        img.src = newSrc;
        break;
      }
    }
  });

  // 移除悬浮属性
  const specialElement = document.getElementById("newspecialtmp");
  if (specialElement) {
    specialElement.removeAttribute("onmouseover");
  }

  // 自动收起分类
  setTimeout(() => {
    const foldButton = document.querySelector("#thread_types .fold");
    if (foldButton) {
      foldButton.click();
    }
  }, 0);
}

// 立即注入主样式，不等待DOM加载
injectStyles(customStyles, STYLE_ID);

// 设置DOM内容修改处理
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", initializeDOMHandlers);
} else {
  initializeDOMHandlers();
}

// 页面完全加载后调用 - 移除样式检查，只保留DOM处理
window.addEventListener("load", () => {
  // 移除对样式注入的检查，只执行DOM操作
  if (!stylesInjected) {
    // 如果样式尚未注入（极端情况），这里补充注入一次
    injectStyles(customStyles, STYLE_ID);
  }


  // 始终执行DOM处理
  initializeDOMHandlers();

  // ====== 主题切换按钮 ======
  // 检查当前是否在编辑器iframe内部
  const isInsideIframe = window.self !== window.top && window.location.href.includes('forum.php?mod=post');
  // 只在主页面上添加主题切换按钮，不在iframe内添加
  if (!isInsideIframe) {
    // 读取上次主题
    let lastTheme = null;
    if (typeof GM_getValue === 'function') {
      lastTheme = GM_getValue('sehuatang_theme', 'pink');
    } else {
      lastTheme = localStorage.getItem('sehuatang_theme') || 'pink';
    }

    // 创建主题切换容器
    const themeContainer = document.createElement('div');
    themeContainer.id = 'theme-switch-container-sehuatang';
    Object.assign(themeContainer.style, {
      zIndex: 1,
      display: 'flex',
      flexDirection: 'row', // 改为水平排列
      alignItems: 'center',
      padding: '6px', // 调整内边距
    });

    // 创建"主题"文字标签 (可选，如果空间不足可以考虑移除或简化)
    const themeLabel = document.createElement('div');
    themeLabel.innerText = '主题'; // 清空文字，如果需要可以保留 "主题:"
    Object.assign(themeLabel.style, {
      fontSize: '14px', // 减小字体
      color: 'var(--primary-font-color)',
      marginRight: '5px', // 调整右边距
      fontWeight: 'normal' // 调整字重
    });
    themeContainer.appendChild(themeLabel);

    // 创建圆点容器
    const dotsContainer = document.createElement('div');
    Object.assign(dotsContainer.style, {
      display: 'flex',
      flexDirection: 'row', // 改为水平排列
      gap: '8px' // 调整圆点间距
    });
    themeContainer.appendChild(dotsContainer);

    // 创建默认主题圆点 (粉色)
    const defaultDot = document.createElement('div');
    defaultDot.className = 'theme-dot default-theme-dot';
    Object.assign(defaultDot.style, {
      width: '10px',
      height: '10px',
      borderRadius: '50%',
      background: '#E45E6B',
      cursor: 'pointer',
      marginLeft: '5px',
      border: '1px solid #fff'
    });
    defaultDot.title = '粉色主题';

    // 创建绿色主题圆点
    const greenDot = document.createElement('div');
    greenDot.className = 'theme-dot green-theme-dot';
    Object.assign(greenDot.style, {
      width: '10px',
      height: '10px',
      borderRadius: '50%',
      background: '#86B2BD',
      cursor: 'pointer',
      border: '1px solid #fff'
    });
    greenDot.title = '绿色主题';

    // 创建浅色主题圆点
    const lightDot = document.createElement('div');
    lightDot.className = 'theme-dot light-theme-dot';
    Object.assign(lightDot.style, {
      width: '10px',
      height: '10px',
      borderRadius: '50%',
      background: '#9CA3AF',
      cursor: 'pointer',
      border: '1px solid #fff'
    });
    lightDot.title = '浅色主题';

    // 创建暗色主题圆点
    const darkDot = document.createElement('div');
    darkDot.className = 'theme-dot dark-theme-dot';
    Object.assign(darkDot.style, {
      width: '10px',
      height: '10px',
      borderRadius: '50%',
      background: '#000',
      cursor: 'pointer',
      border: '1px solid rgb(255, 255, 255)'
    });
    darkDot.title = '暗色主题';

    // 高亮当前选中的主题
    if (document.documentElement.classList.contains('theme-green')) {
      Object.assign(greenDot.style, {
        transform: 'scale(1.0)'
      });
    } else if (document.documentElement.classList.contains('theme-light')) {
      Object.assign(lightDot.style, {
        transform: 'scale(1.0)'
      });
    } else if (document.documentElement.classList.contains('theme-dark')) {
      Object.assign(darkDot.style, {
        transform: 'scale(1.0)'
      });
    } else {
      Object.assign(defaultDot.style, {
        transform: 'scale(1.0)'
      });
    }

    // 点击粉色圆点切换主题
    defaultDot.onclick = function() {
      document.documentElement.classList.add('theme-pink');
      document.documentElement.classList.remove('theme-green', 'theme-light', 'theme-dark');
      if (typeof GM_setValue === 'function') {
        GM_setValue('sehuatang_theme', 'pink');
      } else {
        localStorage.setItem('sehuatang_theme', 'pink');
      }

      // 高亮粉色圆点
      Object.assign(defaultDot.style, {
        transform: 'scale(1.0)'
      });
      Object.assign(greenDot.style, {
        transform: 'scale(1.0)'
      });
      Object.assign(lightDot.style, {
        transform: 'scale(1.0)'
      });
      Object.assign(darkDot.style, {
        transform: 'scale(1.0)'
      });
    };

    // 点击绿色圆点切换主题
    greenDot.onclick = function() {
      document.documentElement.classList.remove('theme-pink', 'theme-light', 'theme-dark');
      document.documentElement.classList.add('theme-green');
      if (typeof GM_setValue === 'function') {
        GM_setValue('sehuatang_theme', 'green');
      } else {
        localStorage.setItem('sehuatang_theme', 'green');
      }

      // 高亮绿色圆点
      Object.assign(greenDot.style, {
        transform: 'scale(1.0)'
      });
      Object.assign(defaultDot.style, {
        transform: 'scale(1.0)'
      });
      Object.assign(lightDot.style, {
        transform: 'scale(1.0)'
      });
      Object.assign(darkDot.style, {
        transform: 'scale(1.0)'
      });
    };

    // 点击浅色圆点切换主题
    lightDot.onclick = function() {
      document.documentElement.classList.remove('theme-pink', 'theme-green', 'theme-dark');
      document.documentElement.classList.add('theme-light');
      if (typeof GM_setValue === 'function') {
        GM_setValue('sehuatang_theme', 'light');
      } else {
        localStorage.setItem('sehuatang_theme', 'light');
      }

      // 高亮浅色圆点
      Object.assign(lightDot.style, {
        transform: 'scale(1.0)'
      });
      Object.assign(defaultDot.style, {
        transform: 'scale(1.0)'
      });
      Object.assign(greenDot.style, {
        transform: 'scale(1.0)'
      });
      Object.assign(darkDot.style, {
        transform: 'scale(1.0)'
      });
    };

    // 点击暗色圆点切换主题
    darkDot.onclick = function() {
      document.documentElement.classList.remove('theme-pink', 'theme-green', 'theme-light');
      document.documentElement.classList.add('theme-dark');
      if (typeof GM_setValue === 'function') {
        GM_setValue('sehuatang_theme', 'dark');
      } else {
        localStorage.setItem('sehuatang_theme', 'dark');
      }

      // 高亮暗色圆点
      Object.assign(darkDot.style, {
        transform: 'scale(1.0)'
      });
      Object.assign(defaultDot.style, {
        transform: 'scale(1.0)'
      });
      Object.assign(greenDot.style, {
        transform: 'scale(1.0)'
      });
      Object.assign(lightDot.style, {
        transform: 'scale(1.0)'
      });
    };

    // 添加圆点到容器
    dotsContainer.appendChild(defaultDot);
    dotsContainer.appendChild(greenDot);
    dotsContainer.appendChild(lightDot);
    dotsContainer.appendChild(darkDot);

    // 添加容器到页面
    // document.body.appendChild(themeContainer); // 旧的插入方式
    const targetElement = document.getElementById('mn_Neaf3');
    if (targetElement && targetElement.parentNode) {
        targetElement.parentNode.insertBefore(themeContainer, targetElement.nextSibling);
    } else {
        // 如果目标元素未找到，则回退到 body 末尾
        document.body.appendChild(themeContainer);
    }
  }
  // ====== 主题切换按钮 END ======

  // 显示页面
  document.documentElement.classList.add('sht-script-ready');
});

  // --- MutationObserver to enable dragging for dynamic popups ---
  function initDragObserver() {
    const targetNode = document.body;
    if (!targetNode) {
        // Body not ready for observer, will retry.
        // Use ready function to ensure body exists
        ready(initDragObserver);
        return;
    }

    const config = { childList: true };

    const callback = function(mutationsList, observer) {
        for(const mutation of mutationsList) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    // Check if the added node itself matches or contains the target
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        if (node.matches('#fwin_dialog, #e_image_menu, #e_attach_menu, .fwinmask')) {
                            enableDrag(node);
                        } else {
                            // Also check descendants in case the popup is wrapped
                            const popup = node.querySelector('#fwin_dialog, #e_image_menu, #e_attach_menu, .fwinmask');
                            if (popup) {
                                enableDrag(popup);
                            }
                        }
                    }
                });
            }
        }
    };

    const observer = new MutationObserver(callback);
    observer.observe(targetNode, config);
    // Drag observer started

    // Also check for already existing popups when the script runs
    document.querySelectorAll('#fwin_dialog, #e_image_menu, #e_attach_menu, .fwinmask').forEach(enableDrag);
  }

  // Initialize the observer after the main DOM is likely ready
  ready(initDragObserver);
  // --- End of MutationObserver setup ---

})(); // 补全主 IIFE 的闭合


(function() {
    'use strict';

    // 检查是否为帖子列表页面（URL检查部分）
    function isForumListPageByURL() {
        const url = window.location.href;
        const pathname = window.location.pathname;

        // 检查URL是否匹配论坛列表页面模式
        const isForumPage = (
            // forum-数字.html 或 forum-数字-数字.html 格式
            /\/forum-\d+(-\d+)?\.html/.test(pathname) ||
            // forum.php?mod=forumdisplay 格式
            (pathname.includes('forum.php') && url.includes('mod=forumdisplay'))
        );

        // 排除帖子详情页面
        const isThreadPage = (
            // thread-数字-数字-数字.html 格式
            /\/thread-\d+-\d+-\d+\.html/.test(pathname) ||
            // viewthread.php 格式
            pathname.includes('viewthread.php') ||
            // forum.php?mod=viewthread 格式
            (pathname.includes('forum.php') && url.includes('mod=viewthread'))
        );

        return isForumPage && !isThreadPage;
    }

    // 完整的页面检查（包括DOM元素检查）
    function isForumListPage() {
        // 先检查URL
        if (!isForumListPageByURL()) {
            return false;
        }

        // 检查页面是否有帖子列表的关键元素
        const hasThreadList = document.getElementById('threadlist');
        return hasThreadList;
    }

    // 早期URL检查，如果URL不匹配就直接退出
    if (!isForumListPageByURL()) {
        return;
    }


    // 等待DOM准备就绪的函数
    function waitForDOM(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    // 等待特定元素出现的函数
    function waitForElement(selector, callback, timeout = 10000) {
        const element = document.querySelector(selector);
        if (element) {
            callback(element);
            return;
        }

        const observer = new MutationObserver((mutations, obs) => {
            const element = document.querySelector(selector);
            if (element) {
                obs.disconnect();
                callback(element);
            }
        });

        observer.observe(document.body || document.documentElement, {
            childList: true,
            subtree: true
        });

        // 设置超时
        setTimeout(() => {
            observer.disconnect();
        }, timeout);
    }

    // 主初始化函数
    function initializeScript() {
        // 再次检查是否为帖子列表页面
        if (!isForumListPage()) {
            return;
        }

        // 移除早期样式
        const earlyStyle = document.getElementById('discuz-redesign-early-styles');
        if (earlyStyle) {
            earlyStyle.remove();
        }

        // 基本样式
    const style = document.createElement('style');
    style.textContent = `
/* 标签展开/折叠动画 */
        .unfold, .fold {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .unfold:hover, .fold:hover {
            transform: scale(1.05);
            opacity: 0.8;
        }

        /* 通用展开/折叠内容动画 */
        .collapsible-item {
            overflow: hidden;
            transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1),
                        opacity 0.3s ease;
        }

        .collapsible-item.collapsed {
            max-height: 0;
            opacity: 0;
        }
#wp .wp {
    width: auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.5);
    width: 920px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 1px 20px rgba(0, 0, 0, 0.05) !important;
}
#wp > .wp {
  display: none;
}

        /* 容器样式 */
        .redesigned-thread-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        /* 帖子卡片样式 */
.thread-card {
    display: flex;
    background: var(--list-color2);
    border-radius: 8px;
    padding: 18px;
    border: 1px solid var(--list-border);
}

        /* 置顶帖样式 */
.thread-card.sticky {
    background: var(--list-color2);
    border-radius: 8px;
    padding: 10px;
    border: 1px solid var(--list-border);
}

        /* 置顶帖隐藏头像 */
        .thread-card.sticky .thread-avatar {
            display: none;
        }

        /* 置顶帖内容垂直居中 */
        .thread-card.sticky {
            align-items: center;
        }

        .thread-card.sticky .thread-content {
            display: flex;
            align-items: center;
        }

        /* 头像区域 */
        .thread-avatar {
            margin-right: 15px;
            flex-shrink: 0;
        }

.thread-avatar img {
	width: 42px;
	height: 42px;
	border-radius: 10px;
	border: 3px solid var(--avatar-border);
	box-shadow: 0 1px 6px rgba(0, 0, 0, 0.3) !important;
	object-fit: cover !important;
}
        /* 内容区域 */
        .thread-content {
            flex-grow: 1;
        }

        .thread-title {
            font-size: 14px;
            font-weight: bold;
        }

.thread-title em a {
	font-size: 10px;
	background: var(--tag) !important;
	padding: 2px 6px;
	border-radius: 6px !important;
	color: #fff !important;
	box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05) !important;
	margin-right: 5px;
	display: inline-block;
}

        /* 图标样式调整 */
        .thread-title img {
            margin: 0 2px;
            vertical-align: middle;
        }



        /* New标记样式 */
        .thread-title .xi1 {
            margin-left: 8px;
            color: #ff6b6b;
            font-size: 11px;
            text-decoration: none;
        }

.thread-info {
    display: flex;
    font-size: 11px;
    color: var(--a-font-color);
    margin: 8px 0 0 0;
    opacity: 0.8;
}

.thread-info span {
    margin-right: 20px;
    display: inline-flex;
    align-items: center;
}

        /* 图标样式 */
.thread-author::before {
    content: "" !important;
    margin-right: 4px !important;
    width: 14px !important;
    height: 14px !important;
    display: inline-block !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E") !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    vertical-align: middle !important;
        }

.thread-time::before {
	content: "" !important;
	margin-right: 4px !important;
	width: 14px !important;
	height: 14px !important;
	display: inline-block !important;
	background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNCIgaGVpZ2h0PSIxNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSIjMDAwMDAwIiBkPSJNMTkgNGgtMVYzYzAtLjYtLjQtMS0xLTFzLTEgLjQtMSAxdjFIOFYzYzAtLjYtLjQtMS0xLTFzLTEgLjQtMSAxdjFINUMzLjMgNCAyIDUuMyAyIDd2MWgyMFY3YzAtMS43LTEuMy0zLTMtM00yIDE5YzAgMS43IDEuMyAzIDMgM2gxNGMxLjcgMCAzLTEuMyAzLTN2LTlIMnptMTUtN2MuNiAwIDEgLjQgMSAxcy0uNCAxLTEgMXMtMS0uNC0xLTFzLjQtMSAxLTFtMCA0Yy42IDAgMSAuNCAxIDFzLS40IDEtMSAxcy0xLS40LTEtMXMuNC0xIDEtMW0tNS00Yy42IDAgMSAuNCAxIDFzLS40IDEtMSAxcy0xLS40LTEtMXMuNC0xIDEtMW0wIDRjLjYgMCAxIC40IDEgMXMtLjQgMS0xIDFzLTEtLjQtMS0xcy40LTEgMS0xbS01LTRjLjYgMCAxIC40IDEgMXMtLjQgMS0xIDFzLTEtLjQtMS0xcy40LTEgMS0xbTAgNGMuNiAwIDEgLjQgMSAxcy0uNCAxLTEgMXMtMS0uNC0xLTFzLjQtMSAxLTEiLz48L3N2Zz4=") !important;
	background-size: contain !important;
	background-repeat: no-repeat !important;
	background-position: center !important;
	opacity: 0.8;
  background-color: var(--icon-color);
}

.thread-replies::before {
    content: "" !important;
    margin-right: 4px !important;
    width: 14px !important;
    height: 14px !important;
    display: inline-block !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E") !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    vertical-align: middle !important;
}

.thread-reply-time::before {
    content: "" !important;
    margin-right: 4px !important;
    width: 14px !important;
    height: 14px !important;
    display: inline-block !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z'/%3E%3C/svg%3E") !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    vertical-align: middle !important;
}

.thread-views::before {
    content: "" !important;
    margin-right: 4px !important;
    width: 14px !important;
    height: 14px !important;
    display: inline-block !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E") !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    vertical-align: middle !important;
}

        /* 置顶标记 */
.sticky-tag {
    background-color: #ff6b6b;
    color: white;
    padding: 2px 6px;
    border-radius: 6px;
    font-size: 11px;
    margin-right: 6px;
}

        /* 隐藏原始列表 */
        #threadlist {
            display: none;
        }

        /* 导航栏样式 */
.forum-navigation {
	background: var(--list-color2);
	border-radius: 8px;
	padding: 5px 10px;
	margin-bottom: 15px;
	border: 1px solid var(--list-border) !important;
}

        .forum-navigation .nav-row {
            display: flex;
            align-items: center;
        }

        .forum-navigation .nav-row:last-child {
            margin-bottom: 0;
        }

        .forum-navigation .nav-label {
            font-weight: bold;
            margin-right: 15px;
            min-width: 60px;
            color: var(--a-font-color);
        }

        .forum-navigation .nav-links {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
            line-height: 2;
        }

        .forum-navigation .nav-links a {
            color: #666;
            text-decoration: none;
            padding: 0 8px;
            border-radius: 8px;
            font-size: 13px;
            transition: all 0.2s ease;
        }

        .forum-navigation .nav-links a:hover {
            background: rgba(0, 0, 0, 0.05);
            color: var(--a-font-color);
        }

        .forum-navigation .nav-links a.current {
            background: #ff6b6b;
            color: white;
        }

        .forum-navigation .nav-links .pipe {
            color: #ccc;
            margin: 0 4px;
        }



        /* 折叠功能样式 */
        .forum-navigation .nav-row.collapsible {
            cursor: pointer;
            user-select: none;
        }

        .forum-navigation .nav-row.collapsible .nav-label::after {
            content: "▼";
            margin-left: 8px;
            font-size: 10px;
            transition: transform 0.2s ease;
            display: inline-block;
        }

        .forum-navigation.collapsed .nav-row.collapsible .nav-label::after {
            transform: rotate(-90deg);
        }

.forum-navigation .nav-row.collapsible-content {
	overflow: hidden;
	max-height: 200px;
	margin-bottom: 5px;
}

        .forum-navigation.collapsed .nav-row.collapsible-content {
            max-height: 0;
            opacity: 0;
            margin-bottom: 0;
        }
    `;
    document.head.appendChild(style);

    // 设置管理
    function getSettings() {
        return {
            autoPagination: GM_getValue('autoPagination', true),
            excludePostOptions: GM_getValue('excludePostOptions', []),
            displayBlockedTips: GM_getValue('displayBlockedTips', true)
        };
    }

    // 创建导航栏
    function createForumNavigation() {
        const navigation = document.createElement('div');
        navigation.className = 'forum-navigation collapsed'; // 默认折叠状态

        // 获取当前URL参数来确定当前选中的选项
        const urlParams = new URLSearchParams(window.location.search);
        const currentFilter = urlParams.get('filter') || '';
        const currentOrderby = urlParams.get('orderby') || '';
        const currentDateline = urlParams.get('dateline') || '';

        // 获取基础URL（不包含筛选参数）
        const baseParams = Array.from(urlParams.entries())
            .filter(([key]) => !['filter', 'orderby', 'dateline', 'digest', 'specialtype'].includes(key))
            .map(([key, value]) => `${key}=${value}`);

        const baseUrl = window.location.pathname + (baseParams.length > 0 ? '?' + baseParams.join('&') : '');

        // 构建URL的辅助函数
        const buildUrl = (params) => {
            const separator = baseUrl.includes('?') ? '&' : '?';
            return baseUrl + separator + params;
        };

        navigation.innerHTML = `
            <div class="nav-row collapsible">
                <span class="nav-label">全部主题</span>
                <div class="nav-links">
                    <a href="${buildUrl('filter=lastpost&orderby=lastpost')}" ${currentFilter === 'lastpost' && currentOrderby === 'lastpost' ? 'class="current"' : ''}>最新</a>
                    <span class="pipe">|</span>
                    <a href="${buildUrl('filter=heat&orderby=heats')}" ${currentFilter === 'heat' && currentOrderby === 'heats' ? 'class="current"' : ''}>热门</a>
                    <span class="pipe">|</span>
                    <a href="${buildUrl('filter=hot')}" ${currentFilter === 'hot' ? 'class="current"' : ''}>热帖</a>
                    <span class="pipe">|</span>
                    <a href="${buildUrl('filter=digest&digest=1&orderby=dateline')}" ${currentFilter === 'digest' ? 'class="current"' : ''}>精华</a>
                </div>
            </div>
            <div class="nav-row collapsible-content">
                <span class="nav-label">排序:</span>
                <div class="nav-links">
                    <a href="${buildUrl('filter=author&orderby=dateline')}" ${currentFilter === 'author' && currentOrderby === 'dateline' ? 'class="current"' : ''}>发帖时间</a>
                    <span class="pipe">|</span>
                    <a href="${buildUrl('filter=reply&orderby=replies')}" ${currentFilter === 'reply' && currentOrderby === 'replies' ? 'class="current"' : ''}>回复/查看</a>
                    <span class="pipe">|</span>
                    <a href="${buildUrl('filter=reply&orderby=views')}" ${currentFilter === 'reply' && currentOrderby === 'views' ? 'class="current"' : ''}>查看</a>
                </div>
            </div>
            <div class="nav-row collapsible-content">
                <span class="nav-label">时间:</span>
                <div class="nav-links">
                    <a href="${buildUrl('orderby=dateline&filter=dateline')}" ${!currentDateline ? 'class="current"' : ''}>全部时间</a>
                    <span class="pipe">|</span>
                    <a href="${buildUrl('orderby=dateline&filter=dateline&dateline=86400')}" ${currentDateline === '86400' ? 'class="current"' : ''}>一天</a>
                    <span class="pipe">|</span>
                    <a href="${buildUrl('orderby=dateline&filter=dateline&dateline=172800')}" ${currentDateline === '172800' ? 'class="current"' : ''}>两天</a>
                    <span class="pipe">|</span>
                    <a href="${buildUrl('orderby=dateline&filter=dateline&dateline=604800')}" ${currentDateline === '604800' ? 'class="current"' : ''}>一周</a>
                    <span class="pipe">|</span>
                    <a href="${buildUrl('orderby=dateline&filter=dateline&dateline=2592000')}" ${currentDateline === '2592000' ? 'class="current"' : ''}>一个月</a>
                    <span class="pipe">|</span>
                    <a href="${buildUrl('orderby=dateline&filter=dateline&dateline=7948800')}" ${currentDateline === '7948800' ? 'class="current"' : ''}>三个月</a>
                </div>
            </div>
        `;

        // 添加折叠/展开功能
        const collapsibleRow = navigation.querySelector('.nav-row.collapsible');
        if (collapsibleRow) {
            collapsibleRow.addEventListener('click', function(e) {
                // 防止点击链接时触发折叠
                if (e.target.tagName === 'A' || e.target.closest('.nav-links')) {
                    return;
                }

                // 切换折叠状态
                navigation.classList.toggle('collapsed');

                // 保存折叠状态到本地存储
                const isCollapsed = navigation.classList.contains('collapsed');
                GM_setValue('navigationCollapsed', isCollapsed);
            });
        }

        // 从本地存储恢复折叠状态
        const savedCollapsedState = GM_getValue('navigationCollapsed', true); // 默认折叠
        if (savedCollapsedState) {
            navigation.classList.add('collapsed');
        } else {
            navigation.classList.remove('collapsed');
        }

        return navigation;
    }



    // 根据space-uid生成头像地址
    function generateAvatarUrl(spaceUid) {
        // 如果没有spaceUid，返回默认头像
        if (!spaceUid) {
            return 'https://ttou.j03og.app/uc_server/images/noavatar_small.gif';
        }

        // 将uid转换为9位数字字符串，前面补0
        const uidStr = spaceUid.toString().padStart(9, '0');

        // 按每两位分组：000/42/81/19
        // 对于428119，补0后变成000428119，分组为：000/42/81/19
        const part1 = uidStr.slice(0, 3);   // 000
        const part2 = uidStr.slice(3, 5);   // 42
        const part3 = uidStr.slice(5, 7);   // 81
        const part4 = uidStr.slice(7, 9);   // 19

        // 构建头像URL
        const baseUrl = window.location.origin;
        return `${baseUrl}/uc_server/data/avatar/${part1}/${part2}/${part3}/${part4}_avatar_middle.jpg`;
    }

    // 提取space-uid
    function extractSpaceUid(row) {
        // 方法1: 查找作者链接中的space-uid（旧格式）
        const authorLink = row.querySelector('td.by cite a');
        if (authorLink && authorLink.href) {
            // 尝试匹配 space-uid-数字 格式
            let match = authorLink.href.match(/space-uid-(\d+)/);
            if (match) {
                const uid = parseInt(match[1]);
                return uid;
            }

            // 尝试匹配 uid=数字 格式（新格式）
            match = authorLink.href.match(/[?&]uid=(\d+)/);
            if (match) {
                const uid = parseInt(match[1]);
                return uid;
            }
        }

        // 方法2: 查找任何包含uid的链接
        const allLinks = row.querySelectorAll('a[href*="uid"]');
        for (const link of allLinks) {
            if (link.href) {
                // 尝试匹配 space-uid-数字 格式
                let match = link.href.match(/space-uid-(\d+)/);
                if (match) {
                    const uid = parseInt(match[1]);
                    return uid;
                }

                // 尝试匹配 uid=数字 格式
                match = link.href.match(/[?&]uid=(\d+)/);
                if (match) {
                    const uid = parseInt(match[1]);
                    return uid;
                }
            }
        }

        // 未找到任何uid
        return null;
    }

    // 提取帖子ID的函数
    function extractThreadId(link) {
        // 匹配 thread-123456-1-1.html 格式
        const threadMatch = link.match(/thread-(\d+)-/);
        if (threadMatch) {
            return threadMatch[1];
        }

        // 匹配 tid=123456 格式
        const tidMatch = link.match(/tid=(\d+)/);
        if (tidMatch) {
            return tidMatch[1];
        }

        return null;
    }

    // 处理帖子行函数
    function processThreadRow(row, isSticky = false) {
        // 提取帖子信息
        const titleElement = row.querySelector('a.s.xst');
        if (!titleElement) return null;

        const title = titleElement.textContent;
        const link = titleElement.href;
        // 保留完整的链接HTML，包括样式
        const titleLinkHtml = titleElement.outerHTML;

        // 提取帖子ID用于唯一标识
        const threadId = extractThreadId(link);

        // 提取作者信息
        const authorElement = row.querySelector('td.by cite a');
        const author = authorElement ? authorElement.textContent : '未知作者';
        // 保留完整的作者链接HTML，但移除颜色样式
        let authorLinkHtml = author;
        if (authorElement) {
            // 克隆元素并移除style属性中的颜色
            const clonedElement = authorElement.cloneNode(true);
            clonedElement.removeAttribute('style');
            authorLinkHtml = clonedElement.outerHTML;
        }

        // 提取space-uid并生成头像地址
        const spaceUid = extractSpaceUid(row);
        const avatarUrl = generateAvatarUrl(spaceUid);

        // 提取发帖时间和回复时间
        const byElements = row.querySelectorAll('td.by');
        let postTime = '';
        let replyTime = '';

        if (byElements.length >= 1) {
            // 第一个td.by包含发帖时间
            const postTimeElement = byElements[0].querySelector('em span');
            postTime = postTimeElement ? (postTimeElement.textContent || postTimeElement.getAttribute('title') || '') : '';
        }

        if (byElements.length >= 2) {
            // 第二个td.by包含最后回复时间
            // 尝试多种选择器来获取回复时间
            let replyTimeElement = byElements[1].querySelector('em span') ||
                                 byElements[1].querySelector('em a span') ||
                                 byElements[1].querySelector('em a');

            if (replyTimeElement) {
                // 优先获取textContent（相对时间），然后是title属性（具体时间）
                replyTime = replyTimeElement.textContent || replyTimeElement.getAttribute('title') || '';
            } else {
                // 如果都没找到，尝试直接从em元素获取
                const emElement = byElements[1].querySelector('em');
                replyTime = emElement ? (emElement.textContent || '') : '';
            }
        }

        // 提取回复数和查看数
        const replyElement = row.querySelector('td.num a');
        const viewElement = row.querySelector('td.num em');
        const replies = replyElement ? replyElement.textContent : '0';
        const views = viewElement ? viewElement.textContent : '0';

        // 提取分类标签并去掉方括号和字体颜色
        const categoryElement = row.querySelector('th em');
        let categoryHtml = '';
        if (categoryElement) {
            // 克隆元素以避免修改原始DOM
            const clonedElement = categoryElement.cloneNode(true);

            // 去除所有font标签的color属性
            const fontElements = clonedElement.querySelectorAll('font[color]');
            fontElements.forEach(font => {
                font.removeAttribute('color');
            });

            // 获取处理后的HTML并去掉方括号
            categoryHtml = clonedElement.outerHTML.replace(/\[/g, '').replace(/\]/g, '');
        }

        // 提取所有图标（精华、附件、热度等）
        const titleCell = row.querySelector('th');
        const icons = titleCell ? titleCell.querySelectorAll('img') : [];
        let iconsHtml = '';
        icons.forEach(icon => {
            if (icon.src && !icon.src.includes('folder_')) { // 排除文件夹图标
                iconsHtml += icon.outerHTML + ' ';
            }
        });

        // 不显示页数链接
        const pagesHtml = '';

        // 提取New标记
        const newElement = row.querySelector('a.xi1');
        const newHtml = newElement ? newElement.outerHTML : '';

        // 创建卡片元素
        const card = document.createElement('div');
        card.className = 'thread-card' + (isSticky ? ' sticky' : '');

        // 添加唯一标识，用于防止重复
        if (threadId) {
            card.setAttribute('data-thread-id', threadId);
        }

        // 构建卡片HTML
        if (isSticky) {
            // 置顶帖：只显示标题，不显示其他信息
            card.innerHTML = `
                <div class="thread-avatar">
                    <img src="${avatarUrl}" alt="${author}" onerror="this.src='https://ttou.j03og.app/uc_server/images/noavatar_small.gif'">
                </div>
                <div class="thread-content">
                    <div class="thread-title">
                        <span class="sticky-tag">置顶</span>
                        <a href="${link}">${title}</a>
                    </div>
                </div>
            `;
        } else {
            // 普通帖：包含完整信息
            card.innerHTML = `
                <div class="thread-avatar">
                    <img src="${avatarUrl}" alt="${author}" onerror="this.src='https://ttou.j03og.app/uc_server/images/noavatar_small.gif'">
                </div>
                <div class="thread-content">
                    <div class="thread-title">
                        ${categoryHtml}
                        ${titleLinkHtml}
                        ${iconsHtml}
                        ${pagesHtml}
                        ${newHtml}
                    </div>
                    <div class="thread-info">
                        <span class="thread-author">${authorLinkHtml}</span>
                        <span class="thread-time">${postTime}</span>
                        <span class="thread-reply-time">${replyTime}</span>
                        <span class="thread-replies">回复: ${replies}</span>
                        <span class="thread-views">查看: ${views}</span>
                    </div>
                </div>
            `;
        }

        return card;
    }



    // 关键字屏蔽功能
    function blockContentByTitleInRedesignedLayout(settings) {
        const { excludePostOptions, displayBlockedTips } = settings;
        const threadCards = document.querySelectorAll('.thread-card');

        if (!excludePostOptions || excludePostOptions.length === 0) {
            return;
        }

        threadCards.forEach(card => {
            // 跳过已经被屏蔽的卡片
            if (card.style.display === 'none' || card.innerHTML.includes('已屏蔽主题')) {
                return;
            }

            const titleElement = card.querySelector('.thread-title a');
            if (!titleElement) return;

            const title = titleElement.textContent;

            // 检查标题是否包含屏蔽关键词
            const shouldBlock = excludePostOptions.some(keyword => {
                if (keyword && keyword.trim()) {
                    return title.toLowerCase().includes(keyword.toLowerCase().trim());
                }
                return false;
            });

            if (shouldBlock) {
                if (displayBlockedTips) {
                    // 显示屏蔽提示
                    card.innerHTML = `
                        <div class="thread-content" style="padding: 10px; color: #666; font-style: italic;">
                            已屏蔽主题关键词: ${excludePostOptions.filter(keyword =>
                                keyword && title.toLowerCase().includes(keyword.toLowerCase().trim())
                            ).join(', ')}
                        </div>
                    `;
                } else {
                    // 完全隐藏
                    card.style.display = 'none';
                }
            }
        });
    }

    // 主函数
    function transformThreadList() {
        const originalThreadList = document.getElementById('threadlist');
        if (!originalThreadList) return;

        // 创建导航栏
        const navigation = createForumNavigation();

        // 创建新的容器
        const newThreadList = document.createElement('div');
        newThreadList.className = 'redesigned-thread-list';

        // 获取所有置顶帖子行
        const stickyThreadRows = originalThreadList.querySelectorAll('tbody[id^="stickthread_"]');

        // 处理置顶帖
        stickyThreadRows.forEach(row => {
            const card = processThreadRow(row, true);
            if (card) newThreadList.appendChild(card);
        });

        // 获取所有普通帖子行
        const normalThreadRows = originalThreadList.querySelectorAll('tbody[id^="normalthread_"]');

        // 处理普通帖
        normalThreadRows.forEach(row => {
            const card = processThreadRow(row, false);
            if (card) newThreadList.appendChild(card);
        });

        // 插入导航栏和新列表并隐藏原列表
        originalThreadList.parentNode.insertBefore(navigation, originalThreadList.nextSibling);
        originalThreadList.parentNode.insertBefore(newThreadList, navigation.nextSibling);

        // 获取设置并应用功能
        const settings = getSettings();

        // 延迟执行，确保DOM元素已经完全渲染
        setTimeout(() => {
            // 应用关键字屏蔽功能
            blockContentByTitleInRedesignedLayout(settings);

            // 发送自定义事件通知其他脚本新布局已完成
            const event = new CustomEvent('discuzRedesignComplete', {
                detail: {
                    message: '新布局已完成',
                    timestamp: Date.now(),
                    threadCount: document.querySelectorAll('.thread-card').length
                }
            });
            document.dispatchEvent(event);
        }, 100);
    }

        // 页面加载完成后执行转换
        window.addEventListener('load', transformThreadList);
    }

    // 等待DOM准备就绪后初始化脚本
    waitForElement('#threadlist', () => {
        initializeScript();
    });

    // 备用初始化（如果元素等待超时）
    waitForDOM(() => {
        setTimeout(() => {
            if (document.getElementById('threadlist') && !document.querySelector('.redesigned-thread-list')) {
                initializeScript();
            }
        }, 1000);
    });

})();

// ====== 背景图片上传功能 ======
(function() {
    'use strict';

    // 添加油猴菜单命令
    if (typeof GM_registerMenuCommand === 'function') {
        GM_registerMenuCommand('背景图片设置', openBackgroundPanel);
    }

    // 背景图片相关的CSS变量
    const backgroundImageCSS = `
        .gradient-background.custom-image::before {
            background-image: var(--custom-background-image, none) !important;
            background-size: var(--bg-background-size, var(--bg-size, cover));
            background-position: var(--bg-position, center);
            background-repeat: var(--bg-repeat, no-repeat);
            background-attachment: var(--bg-attachment, fixed);
            filter: var(--bg-filter, none);
            transform: var(--bg-transform, none);
            top: var(--bg-top, auto);
            left: var(--bg-left, auto);
            width: var(--bg-width, auto);
            height: var(--bg-height, auto);
            opacity: var(--bg-opacity, 1);
        }

        /* 解决模糊边缘发白问题 */
        .gradient-background.custom-image {
            background: transparent !important;
        }

        .gradient-background.custom-image::before {
            /* 扩大背景图片尺寸以覆盖模糊边缘 */
            top: calc(-1 * var(--edge-expand, 20px)) !important;
            left: calc(-1 * var(--edge-expand, 20px)) !important;
            width: calc(100% + 2 * var(--edge-expand, 20px)) !important;
            height: calc(100% + 2 * var(--edge-expand, 20px)) !important;
        }
    `;

    // 添加背景图片样式
    const bgStyleElement = document.createElement('style');
    bgStyleElement.textContent = backgroundImageCSS;
    document.head.appendChild(bgStyleElement);

    // 打开背景设置面板
    function openBackgroundPanel() {
        // 如果面板已存在，先移除
        const existingPanel = document.getElementById('background-settings-panel');
        if (existingPanel) {
            existingPanel.remove();
        }

        // 创建面板容器
        const panel = document.createElement('div');
        panel.id = 'background-settings-panel';
        panel.innerHTML = `
            <div class="bg-panel-overlay">
                <div class="bg-panel-content">
                    <div class="bg-panel-header">
                        <div class="bg-header-left">
                            <div class="bg-header-icon">🖼️</div>
                            <h3>背景图片设置</h3>
                        </div>
                        <button class="bg-panel-close">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </button>
                    </div>

                    <div class="bg-panel-body">
                        <!-- 图片上传区域 -->
                        <div class="bg-card">
                            <div class="bg-card-header">
                                <span class="bg-card-icon">📁</span>
                                <span class="bg-card-title">图片选择</span>
                            </div>
                            <div class="bg-upload-area" id="bg-upload-area">
                                <input type="file" id="bg-file-input" accept="image/*" hidden>
                                <div class="bg-upload-content">
                                    <div class="bg-upload-icon">📷</div>
                                    <div class="bg-upload-text">点击或拖拽上传图片</div>
                                    <div class="bg-upload-hint">支持 JPG、PNG、GIF 格式</div>
                                </div>
                                <div class="bg-preview" id="bg-preview" style="display: none;">
                                    <div class="bg-preview-overlay">
                                        <button class="bg-preview-change">更换图片</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 基础设置 -->
                        <div class="bg-card">
                            <div class="bg-card-header">
                                <span class="bg-card-icon">⚙️</span>
                                <span class="bg-card-title">基础设置</span>
                            </div>
                            <div class="bg-card-content">
                                <div class="bg-control-group">
                                    <div class="bg-control">
                                        <label class="bg-label">背景尺寸</label>
                                        <select id="bg-size" class="bg-select">
                                            <option value="cover">覆盖 (cover)</option>
                                            <option value="contain">包含 (contain)</option>
                                            <option value="auto">自动 (auto)</option>
                                            <option value="100% 100%">拉伸填充</option>
                                        </select>
                                    </div>
                                    <div class="bg-control">
                                        <label class="bg-label">重复方式</label>
                                        <select id="bg-repeat" class="bg-select">
                                            <option value="no-repeat">不重复</option>
                                            <option value="repeat">重复</option>
                                            <option value="repeat-x">水平重复</option>
                                            <option value="repeat-y">垂直重复</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="bg-control-group">
                                    <div class="bg-control">
                                        <label class="bg-label">透明度</label>
                                        <div class="bg-slider-container">
                                            <input type="range" id="bg-opacity" class="bg-slider" min="0" max="1" step="0.01" value="1">
                                            <span class="bg-slider-value" id="bg-opacity-value">100%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 滤镜效果 -->
                        <div class="bg-card">
                            <div class="bg-card-header">
                                <span class="bg-card-icon">🎨</span>
                                <span class="bg-card-title">滤镜效果</span>
                            </div>
                            <div class="bg-card-content">
                                <div class="bg-control">
                                    <label class="bg-label">模糊效果</label>
                                    <div class="bg-slider-container">
                                        <input type="range" id="blur-slider" class="bg-slider" min="0" max="200" step="1" value="0">
                                        <span class="bg-slider-value" id="blur-value">0px</span>
                                    </div>
                                </div>
                                <div class="bg-control">
                                    <label class="bg-label">亮度调节</label>
                                    <div class="bg-slider-container">
                                        <input type="range" id="brightness-slider" class="bg-slider" min="0" max="200" step="1" value="100">
                                        <span class="bg-slider-value" id="brightness-value">100%</span>
                                    </div>
                                </div>
                                <div class="bg-control">
                                    <label class="bg-label">对比度</label>
                                    <div class="bg-slider-container">
                                        <input type="range" id="contrast-slider" class="bg-slider" min="0" max="200" step="1" value="100">
                                        <span class="bg-slider-value" id="contrast-value">100%</span>
                                    </div>
                                </div>
                                <div class="bg-control">
                                    <label class="bg-label">饱和度</label>
                                    <div class="bg-slider-container">
                                        <input type="range" id="saturate-slider" class="bg-slider" min="0" max="200" step="1" value="100">
                                        <span class="bg-slider-value" id="saturate-value">100%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 高级设置 -->
                        <div class="bg-card">
                            <div class="bg-card-header">
                                <span class="bg-card-icon">🔧</span>
                                <span class="bg-card-title">高级设置</span>
                            </div>
                            <div class="bg-card-content">
                                <div class="bg-control-group">
                                    <div class="bg-control">
                                        <label class="bg-label">顶部位置</label>
                                        <div class="bg-slider-container">
                                            <input type="range" id="bg-top-slider" class="bg-slider" min="-100" max="100" step="1" value="0">
                                            <span class="bg-slider-value" id="bg-top-value">0px</span>
                                        </div>
                                    </div>
                                    <div class="bg-control">
                                        <label class="bg-label">左侧位置</label>
                                        <div class="bg-slider-container">
                                            <input type="range" id="bg-left-slider" class="bg-slider" min="-100" max="100" step="1" value="0">
                                            <span class="bg-slider-value" id="bg-left-value">0px</span>
                                        </div>
                                    </div>
                                    <div class="bg-control">
                                        <label class="bg-label">边缘扩展</label>
                                        <div class="bg-slider-container">
                                            <input type="range" id="edge-expand-slider" class="bg-slider" min="0" max="100" step="1" value="20">
                                            <span class="bg-slider-value" id="edge-expand-value">20px</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-control-group">
                                    <div class="bg-control">
                                        <label class="bg-label">宽度缩放</label>
                                        <div class="bg-slider-container">
                                            <input type="range" id="bg-width-slider" class="bg-slider" min="50" max="200" step="1" value="100">
                                            <span class="bg-slider-value" id="bg-width-value">100%</span>
                                        </div>
                                    </div>
                                    <div class="bg-control">
                                        <label class="bg-label">高度缩放</label>
                                        <div class="bg-slider-container">
                                            <input type="range" id="bg-height-slider" class="bg-slider" min="50" max="200" step="1" value="100">
                                            <span class="bg-slider-value" id="bg-height-value">100%</span>
                                        </div>
                                    </div>
                                    <div class="bg-control">
                                        <label class="bg-label">旋转角度</label>
                                        <div class="bg-slider-container">
                                            <input type="range" id="bg-rotate-slider" class="bg-slider" min="-180" max="180" step="1" value="0">
                                            <span class="bg-slider-value" id="bg-rotate-value">0°</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-control-group">
                                    <div class="bg-control">
                                        <label class="bg-label">整体缩放</label>
                                        <div class="bg-slider-container">
                                            <input type="range" id="bg-scale-slider" class="bg-slider" min="50" max="200" step="1" value="100">
                                            <span class="bg-slider-value" id="bg-scale-value">100%</span>
                                        </div>
                                    </div>
                                    <div class="bg-control">
                                        <label class="bg-label">背景尺寸</label>
                                        <div class="bg-slider-container">
                                            <input type="range" id="bg-size-slider" class="bg-slider" min="50" max="300" step="1" value="100">
                                            <span class="bg-slider-value" id="bg-size-value">100%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 操作按钮 -->
                        <div class="bg-actions">
                            <div class="bg-action-group">
                                <button id="bg-apply-btn" class="bg-btn bg-btn-primary">
                                    <span class="bg-btn-icon">✓</span>
                                    应用设置
                                </button>
                                <button id="bg-reset-btn" class="bg-btn bg-btn-secondary">
                                    <span class="bg-btn-icon">↺</span>
                                    重置背景
                                </button>
                            </div>
                            <div class="bg-action-group">
                                <button id="bg-export-btn" class="bg-btn bg-btn-outline">
                                    <span class="bg-btn-icon">📋</span>
                                    导出CSS
                                </button>
                                <button id="bg-export-file-btn" class="bg-btn bg-btn-outline">
                                    <span class="bg-btn-icon">💾</span>
                                    导出配置
                                </button>
                                <button id="bg-import-btn" class="bg-btn bg-btn-outline">
                                    <span class="bg-btn-icon">📁</span>
                                    导入配置
                                </button>
                                <input type="file" id="bg-import-file" accept=".json" style="display: none;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加面板样式
        const panelStyle = document.createElement('style');
        panelStyle.textContent = `
            #background-settings-panel {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                pointer-events: none;
            }

            .bg-panel-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 20px;
                pointer-events: none;
            }

            .bg-panel-content {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(30px) saturate(180%);
                border-radius: 20px;
                width: 960px;
                max-width: 95vw;
                max-height: 85vh;
                overflow: hidden;
                box-shadow:
                    0 20px 60px rgba(0, 0, 0, 0.15),
                    0 8px 25px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.8);
                border: 1px solid rgba(255, 255, 255, 0.2);
                position: relative;
                pointer-events: auto;
            }

            .bg-panel-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 24px 24px 20px;
                background: rgba(255, 255, 255, 0.1);
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
                cursor: move;
                user-select: none;
            }

            .bg-header-left {
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .bg-header-icon {
                font-size: 20px;
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(99, 102, 241, 0.1);
                border-radius: 8px;
            }

            .bg-panel-header h3 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: #1f2937;
                letter-spacing: -0.025em;
            }

            .bg-panel-close {
                background: rgba(0, 0, 0, 0.05);
                border: none;
                color: #6b7280;
                cursor: pointer;
                padding: 8px;
                width: 32px;
                height: 32px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
            }

            .bg-panel-close:hover {
                background: rgba(239, 68, 68, 0.1);
                color: #ef4444;
                transform: scale(1.05);
            }

            .bg-panel-body {
                padding: 0 24px 20px;
                max-height: calc(85vh - 100px);
                overflow-y: auto;
                scrollbar-width: thin;
                scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
            }

            .bg-panel-body::-webkit-scrollbar {
                width: 6px;
            }

            .bg-panel-body::-webkit-scrollbar-track {
                background: transparent;
            }

            .bg-panel-body::-webkit-scrollbar-thumb {
                background: rgba(0, 0, 0, 0.2);
                border-radius: 3px;
            }

            /* 卡片样式 */
            .bg-card {
                background: rgba(255, 255, 255, 0.7);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 16px;
                margin-bottom: 12px;
                overflow: hidden;
                backdrop-filter: blur(10px);
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            }

            .bg-card-header {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 12px 16px 10px;
                background: rgba(255, 255, 255, 0.1);
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }

            .bg-card-icon {
                font-size: 16px;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .bg-card-title {
                font-size: 14px;
                font-weight: 600;
                color: #374151;
                letter-spacing: -0.025em;
            }

            .bg-card-content {
                padding: 16px;
            }

            /* 上传区域 */
            .bg-upload-area {
                position: relative;
                border: 2px dashed #d1d5db;
                border-radius: 12px;
                padding: 24px 16px;
                text-align: center;
                transition: all 0.3s ease;
                cursor: pointer;
                background: rgba(249, 250, 251, 0.5);
                margin: 12px 16px 16px;
            }

            .bg-upload-area:hover {
                border-color: #6366f1;
                background: rgba(99, 102, 241, 0.02);
            }

            .bg-upload-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 8px;
            }

            .bg-upload-icon {
                font-size: 32px;
                opacity: 0.6;
            }

            .bg-upload-text {
                font-size: 14px;
                font-weight: 500;
                color: #374151;
            }

            .bg-upload-hint {
                font-size: 12px;
                color: #6b7280;
            }

            .bg-preview {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
                border-radius: 10px;
                overflow: hidden;
            }

            .bg-preview-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.4);
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .bg-preview:hover .bg-preview-overlay {
                opacity: 1;
            }

            .bg-preview-change {
                background: rgba(255, 255, 255, 0.9);
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                font-size: 12px;
                font-weight: 500;
                color: #374151;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .bg-preview-change:hover {
                background: white;
                transform: scale(1.05);
            }

            /* 控件组 */
            .bg-control-group {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 12px;
                margin-bottom: 12px;
            }

            .bg-control-group:last-child {
                margin-bottom: 0;
            }

            .bg-control {
                display: flex;
                flex-direction: column;
                gap: 6px;
            }

            .bg-label {
                font-size: 13px;
                font-weight: 500;
                color: #374151;
                margin: 0;
            }

            /* 输入框样式 */
            .bg-input, .bg-select {
                width: 100%;
                padding: 10px 12px;
                border: 1.5px solid #e5e7eb;
                border-radius: 8px;
                font-size: 14px;
                background: rgba(255, 255, 255, 0.8);
                transition: all 0.2s ease;
                color: #374151;
            }

            .bg-input:focus, .bg-select:focus {
                outline: none;
                border-color: #6366f1;
                background: rgba(255, 255, 255, 0.95);
                box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            }

            .bg-input::placeholder {
                color: #9ca3af;
            }

            /* 输入组 */
            .bg-input-group {
                display: flex;
                align-items: center;
                position: relative;
            }

            .bg-input-group .bg-input {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
                border-right: none;
            }

            .bg-input-suffix {
                background: rgba(249, 250, 251, 0.8);
                border: 1.5px solid #e5e7eb;
                border-left: none;
                border-top-right-radius: 8px;
                border-bottom-right-radius: 8px;
                padding: 10px 12px;
                font-size: 14px;
                color: #6b7280;
                font-weight: 500;
            }

            /* 滑块容器 */
            .bg-slider-container {
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .bg-slider {
                flex: 1;
                height: 6px;
                border-radius: 3px;
                background: #e5e7eb;
                outline: none;
                -webkit-appearance: none;
                cursor: pointer;
            }

            .bg-slider::-webkit-slider-thumb {
                -webkit-appearance: none;
                appearance: none;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
                cursor: pointer;
                box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
                transition: all 0.2s ease;
            }

            .bg-slider::-webkit-slider-thumb:hover {
                transform: scale(1.1);
                box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
            }

            .bg-slider::-moz-range-thumb {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
                cursor: pointer;
                border: none;
                box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
            }

            .bg-slider-value {
                font-size: 12px;
                font-weight: 600;
                color: #6366f1;
                min-width: 40px;
                text-align: right;
                background: rgba(99, 102, 241, 0.1);
                padding: 4px 8px;
                border-radius: 6px;
            }

            /* 操作按钮区域 */
            .bg-actions {
                margin-top: 16px;
                padding-top: 16px;
                border-top: 1px solid rgba(0, 0, 0, 0.05);
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            .bg-action-group {
                display: flex;
                gap: 8px;
                justify-content: center;
                flex-wrap: wrap;
            }

            .bg-btn {
                display: inline-flex;
                align-items: center;
                gap: 6px;
                padding: 10px 16px;
                border: none;
                border-radius: 10px;
                font-size: 13px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
                text-decoration: none;
                position: relative;
                overflow: hidden;
            }

            .bg-btn-icon {
                font-size: 14px;
            }

            .bg-btn-primary {
                background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
                color: white;
                box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            }

            .bg-btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
            }

            .bg-btn-primary:active {
                transform: translateY(0);
            }

            .bg-btn-secondary {
                background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
                color: white;
                box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
            }

            .bg-btn-secondary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(107, 114, 128, 0.4);
            }

            .bg-btn-outline {
                background: rgba(255, 255, 255, 0.8);
                color: #6b7280;
                border: 1.5px solid #e5e7eb;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            }

            .bg-btn-outline:hover {
                background: rgba(255, 255, 255, 0.95);
                border-color: #6366f1;
                color: #6366f1;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
            }

        `;

        document.head.appendChild(panelStyle);
        document.body.appendChild(panel);

        // 启用面板拖动功能 - 使用标题栏作为拖动句柄
        const panelContent = panel.querySelector('.bg-panel-content');
        const panelHeader = panel.querySelector('.bg-panel-header');
        if (panelContent && panelHeader && typeof enableDrag === 'function') {
            // 设置标题栏为拖动句柄
            panelContent.dataset.dragHandle = '.bg-panel-header';
            enableDrag(panelContent);
        }

        // 初始化面板功能
        initializePanelEvents();
        loadCurrentSettings();
    }

    // 初始化面板事件
    function initializePanelEvents() {
        const panel = document.getElementById('background-settings-panel');
        if (!panel) return;

        // 关闭面板事件
        const closeBtn = panel.querySelector('.bg-panel-close');
        const cancelBtn = panel.querySelector('#bg-cancel-btn');

        [closeBtn, cancelBtn].forEach(btn => {
            if (btn) {
                btn.addEventListener('click', () => panel.remove());
            }
        });

        // 文件选择事件
        const fileInput = panel.querySelector('#bg-file-input');
        const uploadArea = panel.querySelector('#bg-upload-area');
        const preview = panel.querySelector('#bg-preview');
        const uploadContent = uploadArea.querySelector('.bg-upload-content');

        // 点击上传区域触发文件选择
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#6366f1';
            uploadArea.style.background = 'rgba(99, 102, 241, 0.05)';
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#d1d5db';
            uploadArea.style.background = 'rgba(249, 250, 251, 0.5)';
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#d1d5db';
            uploadArea.style.background = 'rgba(249, 250, 251, 0.5)';

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                handleFileUpload(file);
            }
        });

        // 更换图片按钮
        const changeBtn = panel.querySelector('.bg-preview-change');
        if (changeBtn) {
            changeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                fileInput.click();
            });
        }

        function handleFileUpload(file) {
            if (!file.type.startsWith('image/')) {
                showMessage('请选择图片文件！', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                const imageData = e.target.result;

                // 显示预览
                preview.style.backgroundImage = `url(${imageData})`;
                preview.style.display = 'block';
                uploadContent.style.display = 'none';

                // 存储图片数据
                if (typeof GM_setValue === 'function') {
                    GM_setValue('custom_background_image', imageData);
                } else {
                    localStorage.setItem('custom_background_image', imageData);
                }

                showMessage('图片上传成功！', 'success');
            };
            reader.readAsDataURL(file);
        }

        // 滑块事件处理
        const sliders = {
            'bg-opacity': { valueId: 'bg-opacity-value', suffix: '%', multiplier: 100, realtime: true },
            'blur-slider': { valueId: 'blur-value', suffix: 'px', realtime: true },
            'brightness-slider': { valueId: 'brightness-value', suffix: '%', realtime: true },
            'contrast-slider': { valueId: 'contrast-value', suffix: '%', realtime: true },
            'saturate-slider': { valueId: 'saturate-value', suffix: '%', realtime: true },
            'bg-top-slider': { valueId: 'bg-top-value', suffix: 'px', realtime: true },
            'bg-left-slider': { valueId: 'bg-left-value', suffix: 'px', realtime: true },
            'edge-expand-slider': { valueId: 'edge-expand-value', suffix: 'px', realtime: true },
            'bg-width-slider': { valueId: 'bg-width-value', suffix: '%', realtime: true },
            'bg-height-slider': { valueId: 'bg-height-value', suffix: '%', realtime: true },
            'bg-rotate-slider': { valueId: 'bg-rotate-value', suffix: '°', realtime: true },
            'bg-scale-slider': { valueId: 'bg-scale-value', suffix: '%', realtime: true },
            'bg-size-slider': { valueId: 'bg-size-value', suffix: '%', realtime: true }
        };

        Object.entries(sliders).forEach(([sliderId, config]) => {
            const slider = panel.querySelector(`#${sliderId}`);
            const valueSpan = panel.querySelector(`#${config.valueId}`);

            if (slider && valueSpan) {
                // 初始化显示值
                updateSliderValue(slider, valueSpan, config);

                slider.addEventListener('input', (e) => {
                    updateSliderValue(e.target, valueSpan, config);
                    if (config.realtime) {
                        applyBackgroundSettings();
                    }
                });
            }
        });

        function updateSliderValue(slider, valueSpan, config) {
            let value = slider.value;
            if (config.multiplier) {
                value = Math.round(value * config.multiplier);
            }
            valueSpan.textContent = value + config.suffix;
        }



        // 应用设置按钮
        const applyBtn = panel.querySelector('#bg-apply-btn');
        applyBtn.addEventListener('click', applyBackgroundSettings);

        // 重置背景按钮
        const resetBtn = panel.querySelector('#bg-reset-btn');
        resetBtn.addEventListener('click', resetBackground);

        // 导出CSS按钮
        const exportBtn = panel.querySelector('#bg-export-btn');
        exportBtn.addEventListener('click', exportSettings);

        // 导出文件按钮
        const exportFileBtn = panel.querySelector('#bg-export-file-btn');
        exportFileBtn.addEventListener('click', exportToFile);

        // 导入配置按钮
        const importBtn = panel.querySelector('#bg-import-btn');
        const importFile = panel.querySelector('#bg-import-file');
        importBtn.addEventListener('click', () => importFile.click());
        importFile.addEventListener('change', importFromFile);
    }

    // 更新预览滤镜
    function updatePreviewFilter() {
        const panel = document.getElementById('background-settings-panel');
        if (!panel) return;

        const preview = panel.querySelector('#bg-preview');
        const blurSlider = panel.querySelector('#blur-slider');
        const brightnessSlider = panel.querySelector('#brightness-slider');
        const contrastSlider = panel.querySelector('#contrast-slider');
        const saturateSlider = panel.querySelector('#saturate-slider');

        if (!preview || !blurSlider || !brightnessSlider || !contrastSlider || !saturateSlider) {
            return;
        }

        const blur = blurSlider.value;
        const brightness = brightnessSlider.value;
        const contrast = contrastSlider.value;
        const saturate = saturateSlider.value;

        const filterValue = `blur(${blur}px) brightness(${brightness}%) contrast(${contrast}%) saturate(${saturate}%)`;
        preview.style.filter = filterValue;
    }

    // 更新边缘扩展
    function updateEdgeExpansion() {
        const panel = document.getElementById('background-settings-panel');
        if (!panel) return;

        const edgeExpandInput = panel.querySelector('#edge-expand-input');
        if (!edgeExpandInput) return;

        const edgeExpand = edgeExpandInput.value || '20';

        // 实时更新CSS变量
        document.documentElement.style.setProperty('--edge-expand', `${edgeExpand}px`);
    }

    // 应用背景设置
    function applyBackgroundSettings() {
        const panel = document.getElementById('background-settings-panel');
        if (!panel) return;

        // 获取基础设置值
        const bgSize = panel.querySelector('#bg-size').value;
        const bgRepeat = panel.querySelector('#bg-repeat').value;
        const bgOpacity = panel.querySelector('#bg-opacity').value;

        // 获取滤镜滑块值
        const blur = panel.querySelector('#blur-slider').value;
        const brightness = panel.querySelector('#brightness-slider').value;
        const contrast = panel.querySelector('#contrast-slider').value;
        const saturate = panel.querySelector('#saturate-slider').value;

        // 获取高级设置滑块值
        const bgTop = panel.querySelector('#bg-top-slider').value + 'px';
        const bgLeft = panel.querySelector('#bg-left-slider').value + 'px';
        const edgeExpand = panel.querySelector('#edge-expand-slider').value;
        const bgWidthScale = panel.querySelector('#bg-width-slider').value + '%';
        const bgHeightScale = panel.querySelector('#bg-height-slider').value + '%';
        const bgRotate = panel.querySelector('#bg-rotate-slider').value;
        const bgScale = panel.querySelector('#bg-scale-slider').value;
        const bgSizeScale = panel.querySelector('#bg-size-slider').value + '%';

        // 构建变换效果
        let transformParts = [];
        if (bgScale && bgScale !== '100') {
            transformParts.push(`scale(${bgScale / 100})`);
        }
        if (bgRotate && bgRotate !== '0') {
            transformParts.push(`rotate(${bgRotate}deg)`);
        }
        const bgTransform = transformParts.length > 0 ? transformParts.join(' ') : '';

        // 构建背景尺寸
        const bgBackgroundSize = bgSizeScale;

        const filterValue = `blur(${blur}px) brightness(${brightness}%) contrast(${contrast}%) saturate(${saturate}%)`;

        // 获取图片数据
        let imageData = '';
        if (typeof GM_getValue === 'function') {
            imageData = GM_getValue('custom_background_image', '');
        } else {
            imageData = localStorage.getItem('custom_background_image') || '';
        }

        if (imageData) {
            // 应用背景图片
            const gradientBg = document.querySelector('.gradient-background');
            if (gradientBg) {
                gradientBg.classList.add('custom-image');

                // 设置CSS变量
                document.documentElement.style.setProperty('--custom-background-image', `url(${imageData})`);
                document.documentElement.style.setProperty('--bg-size', bgSize);
                document.documentElement.style.setProperty('--bg-position', 'center'); // 默认居中
                document.documentElement.style.setProperty('--bg-repeat', bgRepeat);
                document.documentElement.style.setProperty('--bg-attachment', 'fixed'); // 默认固定
                document.documentElement.style.setProperty('--bg-filter', filterValue);
                document.documentElement.style.setProperty('--edge-expand', `${edgeExpand}px`);

                // 设置新的变换和位置参数
                if (bgTransform) document.documentElement.style.setProperty('--bg-transform', bgTransform);
                if (bgBackgroundSize) document.documentElement.style.setProperty('--bg-background-size', bgBackgroundSize);
                document.documentElement.style.setProperty('--bg-top', bgTop);
                document.documentElement.style.setProperty('--bg-left', bgLeft);
                document.documentElement.style.setProperty('--bg-width', bgWidthScale);
                document.documentElement.style.setProperty('--bg-height', bgHeightScale);
                if (bgOpacity) document.documentElement.style.setProperty('--bg-opacity', bgOpacity);
            }

            // 保存设置
            const settings = {
                size: bgSize,
                position: 'center', // 默认居中
                repeat: bgRepeat,
                attachment: 'fixed', // 默认固定
                transform: bgTransform,
                backgroundSize: bgBackgroundSize,
                top: bgTop,
                left: bgLeft,
                width: bgWidthScale,
                height: bgHeightScale,
                opacity: bgOpacity,
                edgeExpand: edgeExpand,
                rotate: bgRotate,
                scale: bgScale,
                filter: {
                    blur: blur,
                    brightness: brightness,
                    contrast: contrast,
                    saturate: saturate
                }
            };

            if (typeof GM_setValue === 'function') {
                GM_setValue('background_settings', JSON.stringify(settings));
            } else {
                localStorage.setItem('background_settings', JSON.stringify(settings));
            }

            // 显示成功消息
            showMessage('背景设置已应用！', 'success');

            // 保持面板打开状态，不自动关闭
        } else {
            showMessage('请先选择一张图片！', 'error');
        }
    }

    // 重置背景
    function resetBackground() {
        const gradientBg = document.querySelector('.gradient-background');
        if (gradientBg) {
            gradientBg.classList.remove('custom-image');
        }

        // 清除CSS变量
        document.documentElement.style.removeProperty('--custom-background-image');
        document.documentElement.style.removeProperty('--bg-size');
        document.documentElement.style.removeProperty('--bg-position');
        document.documentElement.style.removeProperty('--bg-repeat');
        document.documentElement.style.removeProperty('--bg-attachment');
        document.documentElement.style.removeProperty('--bg-filter');
        document.documentElement.style.removeProperty('--bg-transform');
        document.documentElement.style.removeProperty('--bg-background-size');
        document.documentElement.style.removeProperty('--bg-top');
        document.documentElement.style.removeProperty('--bg-left');
        document.documentElement.style.removeProperty('--bg-width');
        document.documentElement.style.removeProperty('--bg-height');
        document.documentElement.style.removeProperty('--bg-opacity');
        document.documentElement.style.removeProperty('--edge-expand');

        // 清除存储的设置
        if (typeof GM_setValue === 'function') {
            GM_setValue('custom_background_image', '');
            GM_setValue('background_settings', '');
        } else {
            localStorage.removeItem('custom_background_image');
            localStorage.removeItem('background_settings');
        }

        showMessage('背景已重置为默认！', 'success');

        // 关闭面板
        const panel = document.getElementById('background-settings-panel');
        if (panel) panel.remove();
    }

    // 加载当前设置到面板
    function loadCurrentSettings() {
        const panel = document.getElementById('background-settings-panel');
        if (!panel) return;

        // 加载图片
        let imageData = '';
        if (typeof GM_getValue === 'function') {
            imageData = GM_getValue('custom_background_image', '');
        } else {
            imageData = localStorage.getItem('custom_background_image') || '';
        }

        if (imageData) {
            const preview = panel.querySelector('#bg-preview');
            const uploadContent = panel.querySelector('.bg-upload-content');

            if (preview && uploadContent) {
                preview.style.backgroundImage = `url(${imageData})`;
                preview.style.display = 'block';
                uploadContent.style.display = 'none';
            }
        }

        // 加载设置
        let settings = {};
        try {
            const settingsStr = typeof GM_getValue === 'function'
                ? GM_getValue('background_settings', '{}')
                : localStorage.getItem('background_settings') || '{}';
            settings = JSON.parse(settingsStr);
        } catch (e) {
            settings = {};
        }

        // 应用设置到面板控件
        if (settings.size) panel.querySelector('#bg-size').value = settings.size;
        if (settings.repeat) panel.querySelector('#bg-repeat').value = settings.repeat;
        if (settings.transform) panel.querySelector('#bg-transform').value = settings.transform;
        if (settings.backgroundSize) panel.querySelector('#bg-background-size').value = settings.backgroundSize;
        if (settings.top) panel.querySelector('#bg-top').value = settings.top;
        if (settings.left) panel.querySelector('#bg-left').value = settings.left;
        if (settings.width) panel.querySelector('#bg-width').value = settings.width;
        if (settings.height) panel.querySelector('#bg-height').value = settings.height;
        if (settings.opacity) panel.querySelector('#bg-opacity').value = settings.opacity;

        if (settings.filter) {
            const { blur = 0, brightness = 100, contrast = 100, saturate = 100 } = settings.filter;

            const blurSlider = panel.querySelector('#blur-slider');
            const blurValue = panel.querySelector('#blur-value');
            if (blurSlider && blurValue) {
                blurSlider.value = blur;
                blurValue.textContent = blur + 'px';
            }

            const brightnessSlider = panel.querySelector('#brightness-slider');
            const brightnessValue = panel.querySelector('#brightness-value');
            if (brightnessSlider && brightnessValue) {
                brightnessSlider.value = brightness;
                brightnessValue.textContent = brightness + '%';
            }

            const contrastSlider = panel.querySelector('#contrast-slider');
            const contrastValue = panel.querySelector('#contrast-value');
            if (contrastSlider && contrastValue) {
                contrastSlider.value = contrast;
                contrastValue.textContent = contrast + '%';
            }

            const saturateSlider = panel.querySelector('#saturate-slider');
            const saturateValue = panel.querySelector('#saturate-value');
            if (saturateSlider && saturateValue) {
                saturateSlider.value = saturate;
                saturateValue.textContent = saturate + '%';
            }

            updatePreviewFilter();
        }

        // 更新透明度显示
        if (settings.opacity) {
            const opacitySlider = panel.querySelector('#bg-opacity');
            const opacityValue = panel.querySelector('#bg-opacity-value');
            if (opacitySlider && opacityValue) {
                opacitySlider.value = settings.opacity;
                opacityValue.textContent = Math.round(settings.opacity * 100) + '%';
            }
        }

        // 加载边缘扩展设置
        if (settings.edgeExpand) {
            const edgeExpandInput = panel.querySelector('#edge-expand-input');
            if (edgeExpandInput) {
                edgeExpandInput.value = settings.edgeExpand;
            }
        }
    }

    // 显示消息提示
    function showMessage(text, type = 'info') {
        // 移除已存在的消息
        const existingMsg = document.getElementById('bg-message');
        if (existingMsg) existingMsg.remove();

        const message = document.createElement('div');
        message.id = 'bg-message';
        message.textContent = text;

        const colors = {
            success: '#4CAF50',
            error: '#f44336',
            info: '#2196F3'
        };

        Object.assign(message.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: colors[type] || colors.info,
            color: 'white',
            padding: '12px 20px',
            borderRadius: '6px',
            zIndex: '10001',
            fontSize: '14px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });

        document.body.appendChild(message);

        // 动画显示
        setTimeout(() => {
            message.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            message.style.transform = 'translateX(100%)';
            setTimeout(() => message.remove(), 300);
        }, 3000);
    }

    // 导出为文件
    function exportToFile() {
        const panel = document.getElementById('background-settings-panel');
        if (!panel) return;

        // 获取图片数据
        let imageData = '';
        if (typeof GM_getValue === 'function') {
            imageData = GM_getValue('custom_background_image', '');
        } else {
            imageData = localStorage.getItem('custom_background_image') || '';
        }

        // 获取当前所有设置值
        const settings = {
            version: '1.0', // 版本号，用于兼容性检查
            timestamp: new Date().toISOString(),
            imageData: imageData, // 包含图片数据
            repeat: panel.querySelector('#bg-repeat').value,
            transform: panel.querySelector('#bg-transform').value,
            backgroundSize: panel.querySelector('#bg-background-size').value,
            top: panel.querySelector('#bg-top').value,
            left: panel.querySelector('#bg-left').value,
            width: panel.querySelector('#bg-width').value,
            height: panel.querySelector('#bg-height').value,
            opacity: panel.querySelector('#bg-opacity').value,
            edgeExpand: panel.querySelector('#edge-expand-input').value || '20',
            filter: {
                blur: panel.querySelector('#blur-slider').value,
                brightness: panel.querySelector('#brightness-slider').value,
                contrast: panel.querySelector('#contrast-slider').value,
                saturate: panel.querySelector('#saturate-slider').value
            }
        };

        // 创建JSON文件并下载
        const jsonData = JSON.stringify(settings, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `background-config-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showMessage('配置文件已导出！', 'success');
    }

    // 导出CSS设置
    function exportSettings() {
        const panel = document.getElementById('background-settings-panel');
        if (!panel) return;

        // 获取当前所有设置值
        const settings = {
            repeat: panel.querySelector('#bg-repeat').value,
            transform: panel.querySelector('#bg-transform').value,
            backgroundSize: panel.querySelector('#bg-background-size').value,
            top: panel.querySelector('#bg-top').value,
            left: panel.querySelector('#bg-left').value,
            width: panel.querySelector('#bg-width').value,
            height: panel.querySelector('#bg-height').value,
            opacity: panel.querySelector('#bg-opacity').value,
            edgeExpand: panel.querySelector('#edge-expand-input').value || '20',
            filter: {
                blur: panel.querySelector('#blur-slider').value,
                brightness: panel.querySelector('#brightness-slider').value,
                contrast: panel.querySelector('#contrast-slider').value,
                saturate: panel.querySelector('#saturate-slider').value
            }
        };

        // 生成CSS代码
        let cssCode = `/* 背景图片参数设置 */\n`;
        cssCode += `.gradient-background.custom-image::before {\n`;

        if (settings.size) cssCode += `    background-size: ${settings.size};\n`;
        if (settings.backgroundSize) cssCode += `    background-size: ${settings.backgroundSize}; /* 自定义尺寸 */\n`;
        cssCode += `    background-position: center; /* 默认居中 */\n`;
        if (settings.repeat) cssCode += `    background-repeat: ${settings.repeat};\n`;
        cssCode += `    background-attachment: fixed; /* 默认固定 */\n`;

        if (settings.transform) cssCode += `    transform: ${settings.transform};\n`;
        if (settings.top) cssCode += `    top: ${settings.top};\n`;
        if (settings.left) cssCode += `    left: ${settings.left};\n`;
        if (settings.width) cssCode += `    width: ${settings.width};\n`;
        if (settings.height) cssCode += `    height: ${settings.height};\n`;
        if (settings.opacity) cssCode += `    opacity: ${settings.opacity};\n`;

        // 滤镜效果
        const filterParts = [];
        if (settings.filter.blur && settings.filter.blur !== '0') filterParts.push(`blur(${settings.filter.blur}px)`);
        if (settings.filter.brightness && settings.filter.brightness !== '100') filterParts.push(`brightness(${settings.filter.brightness}%)`);
        if (settings.filter.contrast && settings.filter.contrast !== '100') filterParts.push(`contrast(${settings.filter.contrast}%)`);
        if (settings.filter.saturate && settings.filter.saturate !== '100') filterParts.push(`saturate(${settings.filter.saturate}%)`);

        if (filterParts.length > 0) {
            cssCode += `    filter: ${filterParts.join(' ')};\n`;
        }

        cssCode += `}\n`;

        // 复制到剪贴板
        navigator.clipboard.writeText(cssCode).then(() => {
            showMessage('CSS参数已复制到剪贴板！', 'success');
        }).catch(() => {
            // 如果剪贴板API不可用，显示在弹窗中
            const exportWindow = window.open('', '_blank', 'width=600,height=400');
            exportWindow.document.write(`
                <html>
                <head><title>背景参数导出</title></head>
                <body>
                    <h3>背景图片CSS参数</h3>
                    <textarea style="width:100%;height:300px;font-family:monospace;">${cssCode}</textarea>
                    <p>请手动复制上面的CSS代码</p>
                </body>
                </html>
            `);
        });
    }

    // 导入配置文件
    function importFromFile(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const settings = JSON.parse(e.target.result);

                // 验证文件格式
                if (!settings.version) {
                    showMessage('无效的配置文件格式！', 'error');
                    return;
                }

                // 应用导入的设置
                applyImportedSettings(settings);
                showMessage('配置已成功导入！', 'success');

            } catch (error) {
                showMessage('配置文件解析失败！请检查文件格式。', 'error');
                console.error('Import error:', error);
            }
        };

        reader.readAsText(file);
        // 清空文件输入，允许重复选择同一文件
        event.target.value = '';
    }

    // 应用导入的设置
    function applyImportedSettings(settings) {
        const panel = document.getElementById('background-settings-panel');
        if (!panel) return;

        // 导入图片数据
        if (settings.imageData) {
            // 保存图片数据
            if (typeof GM_setValue === 'function') {
                GM_setValue('custom_background_image', settings.imageData);
            } else {
                localStorage.setItem('custom_background_image', settings.imageData);
            }

            // 更新预览
            const preview = panel.querySelector('#bg-preview');
            if (preview) {
                preview.style.backgroundImage = `url(${settings.imageData})`;
                preview.innerHTML = '';
            }
        }

        // 应用设置到面板控件
        if (settings.repeat) panel.querySelector('#bg-repeat').value = settings.repeat;
        if (settings.transform) panel.querySelector('#bg-transform').value = settings.transform;
        if (settings.backgroundSize) panel.querySelector('#bg-background-size').value = settings.backgroundSize;
        if (settings.top) panel.querySelector('#bg-top').value = settings.top;
        if (settings.left) panel.querySelector('#bg-left').value = settings.left;
        if (settings.width) panel.querySelector('#bg-width').value = settings.width;
        if (settings.height) panel.querySelector('#bg-height').value = settings.height;
        if (settings.opacity) panel.querySelector('#bg-opacity').value = settings.opacity;

        // 应用边缘扩展设置
        if (settings.edgeExpand) {
            const edgeExpandInput = panel.querySelector('#edge-expand-input');
            if (edgeExpandInput) {
                edgeExpandInput.value = settings.edgeExpand;
            }
        }

        // 应用滤镜设置
        if (settings.filter) {
            const { blur = 0, brightness = 100, contrast = 100, saturate = 100 } = settings.filter;

            const blurSlider = panel.querySelector('#blur-slider');
            const blurValue = panel.querySelector('#blur-value');
            if (blurSlider && blurValue) {
                blurSlider.value = blur;
                blurValue.textContent = blur;
            }

            const brightnessSlider = panel.querySelector('#brightness-slider');
            const brightnessValue = panel.querySelector('#brightness-value');
            if (brightnessSlider && brightnessValue) {
                brightnessSlider.value = brightness;
                brightnessValue.textContent = brightness;
            }

            const contrastSlider = panel.querySelector('#contrast-slider');
            const contrastValue = panel.querySelector('#contrast-value');
            if (contrastSlider && contrastValue) {
                contrastSlider.value = contrast;
                contrastValue.textContent = contrast;
            }

            const saturateSlider = panel.querySelector('#saturate-slider');
            const saturateValue = panel.querySelector('#saturate-value');
            if (saturateSlider && saturateValue) {
                saturateSlider.value = saturate;
                saturateValue.textContent = saturate;
            }

            // 更新预览滤镜
            setTimeout(() => {
                updatePreviewFilter();
            }, 100);
        }

        // 保存导入的设置
        const settingsToSave = {
            position: 'center',
            attachment: 'fixed',
            repeat: settings.repeat,
            transform: settings.transform,
            backgroundSize: settings.backgroundSize,
            top: settings.top,
            left: settings.left,
            width: settings.width,
            height: settings.height,
            opacity: settings.opacity,
            edgeExpand: settings.edgeExpand,
            filter: settings.filter
        };

        if (typeof GM_setValue === 'function') {
            GM_setValue('background_settings', JSON.stringify(settingsToSave));
        } else {
            localStorage.setItem('background_settings', JSON.stringify(settingsToSave));
        }

        // 立即应用导入的设置到页面背景
        applyImportedSettingsToBackground(settings);

        // 更新边缘扩展效果（直接在这里实现，避免函数调用顺序问题）
        if (settings.edgeExpand) {
            document.documentElement.style.setProperty('--edge-expand', `${settings.edgeExpand}px`);
        }
    }

    // 立即应用导入的设置到页面背景
    function applyImportedSettingsToBackground(settings) {
        // 获取图片数据
        let imageData = '';
        if (typeof GM_getValue === 'function') {
            imageData = GM_getValue('custom_background_image', '');
        } else {
            imageData = localStorage.getItem('custom_background_image') || '';
        }

        if (imageData) {
            // 应用背景图片
            const gradientBg = document.querySelector('.gradient-background');
            if (gradientBg) {
                gradientBg.classList.add('custom-image');

                // 设置CSS变量
                document.documentElement.style.setProperty('--custom-background-image', `url(${imageData})`);
                document.documentElement.style.setProperty('--bg-position', 'center'); // 默认居中
                document.documentElement.style.setProperty('--bg-attachment', 'fixed'); // 默认固定

                // 应用所有导入的设置
                if (settings.repeat) document.documentElement.style.setProperty('--bg-repeat', settings.repeat);
                if (settings.transform) document.documentElement.style.setProperty('--bg-transform', settings.transform);
                if (settings.backgroundSize) document.documentElement.style.setProperty('--bg-background-size', settings.backgroundSize);
                if (settings.top) document.documentElement.style.setProperty('--bg-top', settings.top);
                if (settings.left) document.documentElement.style.setProperty('--bg-left', settings.left);
                if (settings.width) document.documentElement.style.setProperty('--bg-width', settings.width);
                if (settings.height) document.documentElement.style.setProperty('--bg-height', settings.height);
                if (settings.opacity) document.documentElement.style.setProperty('--bg-opacity', settings.opacity);

                // 应用滤镜效果
                if (settings.filter) {
                    const { blur = 0, brightness = 100, contrast = 100, saturate = 100 } = settings.filter;
                    const filterValue = `blur(${blur}px) brightness(${brightness}%) contrast(${contrast}%) saturate(${saturate}%)`;
                    document.documentElement.style.setProperty('--bg-filter', filterValue);
                }

                // 应用边缘扩展
                if (settings.edgeExpand) {
                    document.documentElement.style.setProperty('--edge-expand', `${settings.edgeExpand}px`);
                }
            }
        }
    }

    // 页面加载时自动应用保存的背景设置
    function autoApplyBackground() {
        let imageData = '';
        if (typeof GM_getValue === 'function') {
            imageData = GM_getValue('custom_background_image', '');
        } else {
            imageData = localStorage.getItem('custom_background_image') || '';
        }

        if (!imageData) return;

        // 加载设置
        let settings = {};
        try {
            const settingsStr = typeof GM_getValue === 'function'
                ? GM_getValue('background_settings', '{}')
                : localStorage.getItem('background_settings') || '{}';
            settings = JSON.parse(settingsStr);
        } catch (e) {
            settings = {};
        }

        // 等待背景元素创建
        const checkBackground = () => {
            const gradientBg = document.querySelector('.gradient-background');
            if (gradientBg) {
                gradientBg.classList.add('custom-image');

                // 设置CSS变量
                document.documentElement.style.setProperty('--custom-background-image', `url(${imageData})`);

                if (settings.size) document.documentElement.style.setProperty('--bg-size', settings.size);
                document.documentElement.style.setProperty('--bg-position', settings.position || 'center'); // 默认居中
                if (settings.repeat) document.documentElement.style.setProperty('--bg-repeat', settings.repeat);
                document.documentElement.style.setProperty('--bg-attachment', settings.attachment || 'fixed'); // 默认固定

                // 设置新的变换和位置参数
                if (settings.transform) document.documentElement.style.setProperty('--bg-transform', settings.transform);
                if (settings.backgroundSize) document.documentElement.style.setProperty('--bg-background-size', settings.backgroundSize);
                if (settings.top) document.documentElement.style.setProperty('--bg-top', settings.top);
                if (settings.left) document.documentElement.style.setProperty('--bg-left', settings.left);
                if (settings.width) document.documentElement.style.setProperty('--bg-width', settings.width);
                if (settings.height) document.documentElement.style.setProperty('--bg-height', settings.height);
                if (settings.opacity) document.documentElement.style.setProperty('--bg-opacity', settings.opacity);

                if (settings.filter) {
                    const { blur = 0, brightness = 100, contrast = 100, saturate = 100 } = settings.filter;
                    const filterValue = `blur(${blur}px) brightness(${brightness}%) contrast(${contrast}%) saturate(${saturate}%)`;
                    document.documentElement.style.setProperty('--bg-filter', filterValue);
                }

                // 设置边缘扩展
                if (settings.edgeExpand) {
                    document.documentElement.style.setProperty('--edge-expand', `${settings.edgeExpand}px`);
                }
            } else {
                // 如果背景元素还没创建，等待一下再检查
                setTimeout(checkBackground, 100);
            }
        };

        checkBackground();
    }

    // 页面加载完成后自动应用背景
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', autoApplyBackground);
    } else {
        autoApplyBackground();
    }

})();

